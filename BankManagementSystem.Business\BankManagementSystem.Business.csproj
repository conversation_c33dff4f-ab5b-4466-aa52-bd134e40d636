<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BankManagementSystem.Models\BankManagementSystem.Models.csproj" />
    <ProjectReference Include="..\BankManagementSystem.DataAccess\BankManagementSystem.DataAccess.csproj" />
  </ItemGroup>

</Project>

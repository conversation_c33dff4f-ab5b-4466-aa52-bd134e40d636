using System;
using System.Collections.Generic;
using BankManagementSystem.Models.Entities;
using BankManagementSystem.Models.Enums;
using BankManagementSystem.DataAccess.Repositories;

namespace BankManagementSystem.Business.Services
{
    /// <summary>
    /// خدمة إدارة الحسابات المصرفية
    /// </summary>
    public class AccountService
    {
        private readonly AccountRepository _accountRepository;
        private readonly CustomerRepository _customerRepository;
        
        public AccountService()
        {
            _accountRepository = new AccountRepository();
            _customerRepository = new CustomerRepository();
        }
        
        /// <summary>
        /// فتح حساب جديد
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <param name="accountType">نوع الحساب</param>
        /// <param name="initialDeposit">الإيداع الأولي</param>
        /// <param name="branchId">معرف الفرع</param>
        /// <returns>رقم الحساب الجديد أو null في حالة الفشل</returns>
        public string OpenAccount(int customerId, AccountType accountType, decimal initialDeposit, int branchId)
        {
            try
            {
                // التحقق من وجود العميل
                var customer = _customerRepository.GetCustomerByUserId(customerId);
                if (customer == null)
                    return null;
                
                // تحديد الحد الأدنى للرصيد
                decimal minimumBalance = accountType == AccountType.Current ? 1000 : 500;
                
                // التحقق من أن الإيداع الأولي يغطي الحد الأدنى
                if (initialDeposit < minimumBalance)
                    return null;
                
                // توليد رقم الحساب
                string accountNumber = _accountRepository.GenerateAccountNumber(branchId, accountType);
                
                // إنشاء الحساب
                var account = new Account
                {
                    AccountNumber = accountNumber,
                    CustomerId = customer.CustomerId,
                    AccountType = accountType,
                    Balance = initialDeposit,
                    MinimumBalance = minimumBalance,
                    InterestRate = accountType == AccountType.Savings ? 0.02m : null, // 2% للتوفير
                    BranchId = branchId,
                    IsActive = true,
                    OpenDate = DateTime.Now
                };
                
                int accountId = _accountRepository.AddAccount(account);
                
                return accountId > 0 ? accountNumber : null;
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// الحصول على حساب بواسطة رقم الحساب
        /// </summary>
        /// <param name="accountNumber">رقم الحساب</param>
        /// <returns>بيانات الحساب أو null</returns>
        public Account GetAccount(string accountNumber)
        {
            try
            {
                return _accountRepository.GetAccountByNumber(accountNumber);
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// الحصول على حسابات العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>قائمة حسابات العميل</returns>
        public List<Account> GetCustomerAccounts(int customerId)
        {
            try
            {
                return _accountRepository.GetAccountsByCustomerId(customerId);
            }
            catch (Exception)
            {
                return new List<Account>();
            }
        }
        
        /// <summary>
        /// التحقق من صحة رقم الحساب
        /// </summary>
        /// <param name="accountNumber">رقم الحساب</param>
        /// <returns>true إذا كان رقم الحساب صحيح</returns>
        public bool ValidateAccountNumber(string accountNumber)
        {
            if (string.IsNullOrWhiteSpace(accountNumber))
                return false;
            
            // التحقق من طول رقم الحساب (يجب أن يكون 12 رقم)
            if (accountNumber.Length != 12)
                return false;
            
            // التحقق من أن الحساب موجود
            var account = GetAccount(accountNumber);
            return account != null && account.IsActive;
        }
        
        /// <summary>
        /// التحقق من إمكانية السحب من الحساب
        /// </summary>
        /// <param name="accountNumber">رقم الحساب</param>
        /// <param name="amount">المبلغ المراد سحبه</param>
        /// <returns>true إذا كان السحب ممكن</returns>
        public bool CanWithdraw(string accountNumber, decimal amount)
        {
            try
            {
                var account = GetAccount(accountNumber);
                if (account == null || !account.IsActive)
                    return false;
                
                // التحقق من أن الرصيد بعد السحب لن يقل عن الحد الأدنى
                return (account.Balance - amount) >= account.MinimumBalance;
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        /// <summary>
        /// الحصول على رصيد الحساب
        /// </summary>
        /// <param name="accountNumber">رقم الحساب</param>
        /// <returns>رصيد الحساب أو null في حالة عدم وجود الحساب</returns>
        public decimal? GetAccountBalance(string accountNumber)
        {
            try
            {
                var account = GetAccount(accountNumber);
                return account?.Balance;
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// تحديث رصيد الحساب
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="newBalance">الرصيد الجديد</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateAccountBalance(int accountId, decimal newBalance)
        {
            try
            {
                return _accountRepository.UpdateAccountBalance(accountId, newBalance);
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        /// <summary>
        /// الحصول على اسم نوع الحساب باللغة العربية
        /// </summary>
        /// <param name="accountType">نوع الحساب</param>
        /// <returns>اسم نوع الحساب باللغة العربية</returns>
        public string GetAccountTypeDisplayName(AccountType accountType)
        {
            return accountType switch
            {
                AccountType.Current => "حساب جاري",
                AccountType.Savings => "حساب توفير",
                _ => "غير محدد"
            };
        }
    }
}

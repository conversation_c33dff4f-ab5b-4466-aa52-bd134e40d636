using System;
using System.Security.Cryptography;
using System.Text;
using BankManagementSystem.Models.Entities;
using BankManagementSystem.Models.Enums;
using BankManagementSystem.DataAccess.Repositories;

namespace BankManagementSystem.Business.Services
{
    /// <summary>
    /// خدمة المصادقة وإدارة المستخدمين
    /// </summary>
    public class AuthenticationService
    {
        private readonly UserRepository _userRepository;
        private User _currentUser;
        
        public AuthenticationService()
        {
            _userRepository = new UserRepository();
        }
        
        /// <summary>
        /// المستخدم الحالي المسجل دخوله
        /// </summary>
        public User CurrentUser => _currentUser;
        
        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>true إذا تم تسجيل الدخول بنجاح</returns>
        public bool Login(string username, string password)
        {
            try
            {
                var user = _userRepository.GetUserByUsername(username);
                if (user == null)
                    return false;
                
                string hashedPassword = HashPassword(password);
                if (user.PasswordHash != hashedPassword)
                    return false;
                
                _currentUser = user;
                _userRepository.UpdateLastLoginDate(user.UserId);
                
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public void Logout()
        {
            _currentUser = null;
        }
        
        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }
        
        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <param name="fullName">الاسم الكامل</param>
        /// <param name="email">البريد الإلكتروني</param>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <param name="role">دور المستخدم</param>
        /// <param name="branchId">معرف الفرع (للموظفين)</param>
        /// <returns>معرف المستخدم الجديد أو -1 في حالة الفشل</returns>
        public int CreateUser(string username, string password, string fullName, string email, string phoneNumber, UserRole role, int? branchId = null)
        {
            try
            {
                // التحقق من عدم وجود مستخدم بنفس الاسم
                var existingUser = _userRepository.GetUserByUsername(username);
                if (existingUser != null)
                    return -1;
                
                var user = new User
                {
                    Username = username,
                    PasswordHash = HashPassword(password),
                    FullName = fullName,
                    Email = email,
                    PhoneNumber = phoneNumber,
                    Role = role,
                    BranchId = branchId,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };
                
                return _userRepository.AddUser(user);
            }
            catch (Exception)
            {
                return -1;
            }
        }
        
        /// <summary>
        /// التحقق من صلاحية المستخدم لتنفيذ عملية معينة
        /// </summary>
        /// <param name="requiredRole">الدور المطلوب</param>
        /// <returns>true إذا كان المستخدم لديه الصلاحية</returns>
        public bool HasPermission(UserRole requiredRole)
        {
            if (_currentUser == null)
                return false;
            
            // رئيس البنك لديه جميع الصلاحيات
            if (_currentUser.Role == UserRole.BankPresident)
                return true;
            
            // مدير الفرع لديه صلاحيات الموظف والعميل
            if (_currentUser.Role == UserRole.BranchManager && 
                (requiredRole == UserRole.BankEmployee || requiredRole == UserRole.Customer))
                return true;
            
            // موظف البنك لديه صلاحيات العميل
            if (_currentUser.Role == UserRole.BankEmployee && requiredRole == UserRole.Customer)
                return true;
            
            // التحقق من تطابق الدور
            return _currentUser.Role == requiredRole;
        }
        
        /// <summary>
        /// التحقق من أن المستخدم مسجل دخوله
        /// </summary>
        /// <returns>true إذا كان المستخدم مسجل دخوله</returns>
        public bool IsLoggedIn()
        {
            return _currentUser != null;
        }
        
        /// <summary>
        /// الحصول على اسم الدور باللغة العربية
        /// </summary>
        /// <param name="role">الدور</param>
        /// <returns>اسم الدور باللغة العربية</returns>
        public string GetRoleDisplayName(UserRole role)
        {
            return role switch
            {
                UserRole.Customer => "عميل",
                UserRole.BankEmployee => "موظف بنك",
                UserRole.BranchManager => "مدير فرع",
                UserRole.BankPresident => "رئيس البنك",
                _ => "غير محدد"
            };
        }
    }
}

using System;
using System.Collections.Generic;
using BankManagementSystem.Models.Entities;
using BankManagementSystem.Models.Enums;
using BankManagementSystem.DataAccess.Repositories;

namespace BankManagementSystem.Business.Services
{
    /// <summary>
    /// خدمة إدارة المعاملات المصرفية
    /// </summary>
    public class TransactionService
    {
        private readonly TransactionRepository _transactionRepository;
        private readonly AccountRepository _accountRepository;
        private readonly AccountService _accountService;
        
        public TransactionService()
        {
            _transactionRepository = new TransactionRepository();
            _accountRepository = new AccountRepository();
            _accountService = new AccountService();
        }
        
        /// <summary>
        /// تنفيذ عملية إيداع
        /// </summary>
        /// <param name="accountNumber">رقم الحساب</param>
        /// <param name="amount">المبلغ</param>
        /// <param name="processedByUserId">معرف المستخدم الذي نفذ العملية</param>
        /// <param name="description">وصف العملية</param>
        /// <returns>رقم المعاملة أو null في حالة الفشل</returns>
        public string ProcessDeposit(string accountNumber, decimal amount, int processedByUserId, string description = null)
        {
            try
            {
                if (amount <= 0)
                    return null;
                
                var account = _accountService.GetAccount(accountNumber);
                if (account == null || !account.IsActive)
                    return null;
                
                decimal balanceBefore = account.Balance;
                decimal balanceAfter = balanceBefore + amount;
                
                // إنشاء المعاملة
                var transaction = new Transaction
                {
                    TransactionNumber = _transactionRepository.GenerateTransactionNumber(),
                    FromAccountId = account.AccountId,
                    TransactionType = TransactionType.Deposit,
                    Amount = amount,
                    BalanceBefore = balanceBefore,
                    BalanceAfter = balanceAfter,
                    Description = description ?? "إيداع نقدي",
                    ProcessedByUserId = processedByUserId,
                    TransactionDate = DateTime.Now,
                    Status = "Completed"
                };
                
                // حفظ المعاملة
                int transactionId = _transactionRepository.AddTransaction(transaction);
                if (transactionId <= 0)
                    return null;
                
                // تحديث رصيد الحساب
                bool balanceUpdated = _accountService.UpdateAccountBalance(account.AccountId, balanceAfter);
                if (!balanceUpdated)
                    return null;
                
                return transaction.TransactionNumber;
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// تنفيذ عملية سحب
        /// </summary>
        /// <param name="accountNumber">رقم الحساب</param>
        /// <param name="amount">المبلغ</param>
        /// <param name="processedByUserId">معرف المستخدم الذي نفذ العملية</param>
        /// <param name="description">وصف العملية</param>
        /// <returns>رقم المعاملة أو null في حالة الفشل</returns>
        public string ProcessWithdrawal(string accountNumber, decimal amount, int processedByUserId, string description = null)
        {
            try
            {
                if (amount <= 0)
                    return null;
                
                var account = _accountService.GetAccount(accountNumber);
                if (account == null || !account.IsActive)
                    return null;
                
                // التحقق من إمكانية السحب
                if (!_accountService.CanWithdraw(accountNumber, amount))
                    return null;
                
                decimal balanceBefore = account.Balance;
                decimal balanceAfter = balanceBefore - amount;
                
                // إنشاء المعاملة
                var transaction = new Transaction
                {
                    TransactionNumber = _transactionRepository.GenerateTransactionNumber(),
                    FromAccountId = account.AccountId,
                    TransactionType = TransactionType.Withdrawal,
                    Amount = amount,
                    BalanceBefore = balanceBefore,
                    BalanceAfter = balanceAfter,
                    Description = description ?? "سحب نقدي",
                    ProcessedByUserId = processedByUserId,
                    TransactionDate = DateTime.Now,
                    Status = "Completed"
                };
                
                // حفظ المعاملة
                int transactionId = _transactionRepository.AddTransaction(transaction);
                if (transactionId <= 0)
                    return null;
                
                // تحديث رصيد الحساب
                bool balanceUpdated = _accountService.UpdateAccountBalance(account.AccountId, balanceAfter);
                if (!balanceUpdated)
                    return null;
                
                return transaction.TransactionNumber;
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// تنفيذ عملية تحويل
        /// </summary>
        /// <param name="fromAccountNumber">رقم الحساب المرسل</param>
        /// <param name="toAccountNumber">رقم الحساب المستقبل</param>
        /// <param name="amount">المبلغ</param>
        /// <param name="processedByUserId">معرف المستخدم الذي نفذ العملية</param>
        /// <param name="description">وصف العملية</param>
        /// <returns>رقم المعاملة أو null في حالة الفشل</returns>
        public string ProcessTransfer(string fromAccountNumber, string toAccountNumber, decimal amount, int processedByUserId, string description = null)
        {
            try
            {
                if (amount <= 0)
                    return null;
                
                if (fromAccountNumber == toAccountNumber)
                    return null;
                
                var fromAccount = _accountService.GetAccount(fromAccountNumber);
                var toAccount = _accountService.GetAccount(toAccountNumber);
                
                if (fromAccount == null || !fromAccount.IsActive || toAccount == null || !toAccount.IsActive)
                    return null;
                
                // التحقق من إمكانية السحب من الحساب المرسل
                if (!_accountService.CanWithdraw(fromAccountNumber, amount))
                    return null;
                
                decimal fromBalanceBefore = fromAccount.Balance;
                decimal fromBalanceAfter = fromBalanceBefore - amount;
                decimal toBalanceBefore = toAccount.Balance;
                decimal toBalanceAfter = toBalanceBefore + amount;
                
                string transactionNumber = _transactionRepository.GenerateTransactionNumber();
                
                // إنشاء معاملة السحب
                var withdrawalTransaction = new Transaction
                {
                    TransactionNumber = transactionNumber,
                    FromAccountId = fromAccount.AccountId,
                    ToAccountId = toAccount.AccountId,
                    TransactionType = TransactionType.Transfer,
                    Amount = amount,
                    BalanceBefore = fromBalanceBefore,
                    BalanceAfter = fromBalanceAfter,
                    Description = description ?? $"تحويل إلى حساب {toAccountNumber}",
                    ProcessedByUserId = processedByUserId,
                    TransactionDate = DateTime.Now,
                    Status = "Completed"
                };
                
                // إنشاء معاملة الإيداع
                var depositTransaction = new Transaction
                {
                    TransactionNumber = transactionNumber + "_IN",
                    FromAccountId = toAccount.AccountId,
                    ToAccountId = fromAccount.AccountId,
                    TransactionType = TransactionType.Transfer,
                    Amount = amount,
                    BalanceBefore = toBalanceBefore,
                    BalanceAfter = toBalanceAfter,
                    Description = description ?? $"تحويل من حساب {fromAccountNumber}",
                    ProcessedByUserId = processedByUserId,
                    TransactionDate = DateTime.Now,
                    Status = "Completed"
                };
                
                // حفظ المعاملات
                int withdrawalId = _transactionRepository.AddTransaction(withdrawalTransaction);
                int depositId = _transactionRepository.AddTransaction(depositTransaction);
                
                if (withdrawalId <= 0 || depositId <= 0)
                    return null;
                
                // تحديث أرصدة الحسابات
                bool fromBalanceUpdated = _accountService.UpdateAccountBalance(fromAccount.AccountId, fromBalanceAfter);
                bool toBalanceUpdated = _accountService.UpdateAccountBalance(toAccount.AccountId, toBalanceAfter);
                
                if (!fromBalanceUpdated || !toBalanceUpdated)
                    return null;
                
                return transactionNumber;
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// الحصول على معاملات الحساب
        /// </summary>
        /// <param name="accountNumber">رقم الحساب</param>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>قائمة المعاملات</returns>
        public List<Transaction> GetAccountTransactions(string accountNumber, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var account = _accountService.GetAccount(accountNumber);
                if (account == null)
                    return new List<Transaction>();
                
                return _transactionRepository.GetAccountTransactions(account.AccountId, startDate, endDate);
            }
            catch (Exception)
            {
                return new List<Transaction>();
            }
        }
        
        /// <summary>
        /// البحث عن معاملة بواسطة رقم المعاملة
        /// </summary>
        /// <param name="transactionNumber">رقم المعاملة</param>
        /// <returns>بيانات المعاملة أو null</returns>
        public Transaction GetTransaction(string transactionNumber)
        {
            try
            {
                return _transactionRepository.GetTransactionByNumber(transactionNumber);
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// الحصول على اسم نوع المعاملة باللغة العربية
        /// </summary>
        /// <param name="transactionType">نوع المعاملة</param>
        /// <returns>اسم نوع المعاملة باللغة العربية</returns>
        public string GetTransactionTypeDisplayName(TransactionType transactionType)
        {
            return transactionType switch
            {
                TransactionType.Deposit => "إيداع",
                TransactionType.Withdrawal => "سحب",
                TransactionType.Transfer => "تحويل",
                TransactionType.Fee => "رسوم",
                _ => "غير محدد"
            };
        }
    }
}

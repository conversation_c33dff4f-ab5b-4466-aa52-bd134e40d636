using System;
using System.Configuration;
using System.Data.SqlClient;

namespace BankManagementSystem.DataAccess
{
    /// <summary>
    /// كلاس إدارة الاتصال بقاعدة البيانات
    /// </summary>
    public class DatabaseConnection
    {
        private static string _connectionString;
        
        /// <summary>
        /// سلسلة الاتصال بقاعدة البيانات
        /// </summary>
        public static string ConnectionString
        {
            get
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    _connectionString = ConfigurationManager.ConnectionStrings["BankDB"]?.ConnectionString
                        ?? "Server=.\\SQLEXPRESS;Database=BankManagementDB;Integrated Security=true;";
                }
                return _connectionString;
            }
            set
            {
                _connectionString = value;
            }
        }
        
        /// <summary>
        /// إنشاء اتصال جديد بقاعدة البيانات
        /// </summary>
        /// <returns>كائن SqlConnection</returns>
        public static SqlConnection GetConnection()
        {
            return new SqlConnection(ConnectionString);
        }
        
        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>true إذا كان الاتصال ناجح، false إذا فشل</returns>
        public static bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        /// <summary>
        /// تنفيذ استعلام SQL وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public static int ExecuteNonQuery(string query, params SqlParameter[] parameters)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    return command.ExecuteNonQuery();
                }
            }
        }
        
        /// <summary>
        /// تنفيذ استعلام SQL وإرجاع قيمة واحدة
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        public static object ExecuteScalar(string query, params SqlParameter[] parameters)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    return command.ExecuteScalar();
                }
            }
        }
    }
}

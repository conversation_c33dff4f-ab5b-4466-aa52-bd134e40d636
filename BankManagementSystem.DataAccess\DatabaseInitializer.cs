using System;
using System.Data.SqlClient;
using System.IO;

namespace BankManagementSystem.DataAccess
{
    /// <summary>
    /// كلاس تهيئة قاعدة البيانات
    /// </summary>
    public class DatabaseInitializer
    {
        /// <summary>
        /// التحقق من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
        /// </summary>
        /// <returns>true إذا تم إنشاء قاعدة البيانات بنجاح أو كانت موجودة مسبقاً</returns>
        public static bool InitializeDatabase()
        {
            try
            {
                // التحقق من الاتصال بقاعدة البيانات
                if (DatabaseConnection.TestConnection())
                {
                    return true; // قاعدة البيانات موجودة ويمكن الوصول إليها
                }
                
                // محاولة إنشاء قاعدة البيانات
                return CreateDatabase();
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(
                    $"خطأ في تهيئة قاعدة البيانات:\n{ex.Message}",
                    "خطأ قاعدة البيانات",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }
        
        /// <summary>
        /// إنشاء قاعدة البيانات من ملف SQL
        /// </summary>
        /// <returns>true إذا تم الإنشاء بنجاح</returns>
        private static bool CreateDatabase()
        {
            try
            {
                // البحث عن ملف إنشاء قاعدة البيانات
                string sqlFilePath = FindDatabaseScript();
                
                if (string.IsNullOrEmpty(sqlFilePath))
                {
                    System.Windows.Forms.MessageBox.Show(
                        "لم يتم العثور على ملف إنشاء قاعدة البيانات (CreateDatabase.sql)",
                        "خطأ",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Error);
                    return false;
                }
                
                // قراءة محتوى ملف SQL
                string sqlScript = File.ReadAllText(sqlFilePath);
                
                // تنفيذ السكريبت
                ExecuteSqlScript(sqlScript);
                
                return true;
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(
                    $"خطأ في إنشاء قاعدة البيانات:\n{ex.Message}",
                    "خطأ",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }
        
        /// <summary>
        /// البحث عن ملف سكريبت قاعدة البيانات
        /// </summary>
        /// <returns>مسار الملف أو null إذا لم يتم العثور عليه</returns>
        private static string FindDatabaseScript()
        {
            string currentDirectory = AppDomain.CurrentDomain.BaseDirectory;
            
            // البحث في المجلدات المحتملة
            string[] possiblePaths = {
                Path.Combine(currentDirectory, "Database", "CreateDatabase.sql"),
                Path.Combine(currentDirectory, "..", "..", "..", "Database", "CreateDatabase.sql"),
                Path.Combine(currentDirectory, "..", "..", "..", "..", "Database", "CreateDatabase.sql"),
                Path.Combine(currentDirectory, "CreateDatabase.sql")
            };
            
            foreach (string path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    return path;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// تنفيذ سكريبت SQL
        /// </summary>
        /// <param name="sqlScript">محتوى السكريبت</param>
        private static void ExecuteSqlScript(string sqlScript)
        {
            // تقسيم السكريبت إلى أوامر منفصلة
            string[] commands = sqlScript.Split(new string[] { "GO" }, StringSplitOptions.RemoveEmptyEntries);
            
            // استخدام اتصال بـ master database لإنشاء قاعدة البيانات
            string masterConnectionString = DatabaseConnection.ConnectionString.Replace("BankManagementDB", "master");
            
            using (var connection = new SqlConnection(masterConnectionString))
            {
                connection.Open();
                
                foreach (string commandText in commands)
                {
                    if (string.IsNullOrWhiteSpace(commandText))
                        continue;
                    
                    using (var command = new SqlCommand(commandText.Trim(), connection))
                    {
                        command.CommandTimeout = 300; // 5 دقائق
                        command.ExecuteNonQuery();
                    }
                }
            }
        }
        
        /// <summary>
        /// التحقق من وجود البيانات الأولية
        /// </summary>
        /// <returns>true إذا كانت البيانات الأولية موجودة</returns>
        public static bool HasInitialData()
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Users WHERE Role = 4"; // رئيس البنك
                var result = DatabaseConnection.ExecuteScalar(query);
                return Convert.ToInt32(result) > 0;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// إنشاء البيانات الأولية إذا لم تكن موجودة
        /// </summary>
        public static void EnsureInitialData()
        {
            if (!HasInitialData())
            {
                CreateInitialData();
            }
        }
        
        /// <summary>
        /// إنشاء البيانات الأولية
        /// </summary>
        private static void CreateInitialData()
        {
            try
            {
                // إنشاء فرع رئيسي إذا لم يكن موجوداً
                string checkBranchQuery = "SELECT COUNT(*) FROM Branches";
                var branchCount = DatabaseConnection.ExecuteScalar(checkBranchQuery);
                
                if (Convert.ToInt32(branchCount) == 0)
                {
                    string insertBranchQuery = @"
                        INSERT INTO Branches (BranchName, BranchCode, Address, City, PhoneNumber)
                        VALUES (N'الفرع الرئيسي', 'MAIN001', N'شارع الملك فهد، الرياض', N'الرياض', '011-1234567')";
                    DatabaseConnection.ExecuteNonQuery(insertBranchQuery);
                }
                
                // إنشاء مستخدم رئيس البنك الافتراضي
                string insertAdminQuery = @"
                    INSERT INTO Users (Username, PasswordHash, FullName, Email, Role, BranchId)
                    VALUES ('admin', 'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', 
                            N'مدير النظام', '<EMAIL>', 4, 1)";
                DatabaseConnection.ExecuteNonQuery(insertAdminQuery);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(
                    $"خطأ في إنشاء البيانات الأولية:\n{ex.Message}",
                    "خطأ",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Warning);
            }
        }
    }
}

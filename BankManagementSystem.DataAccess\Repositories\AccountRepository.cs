using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using BankManagementSystem.Models.Entities;
using BankManagementSystem.Models.Enums;

namespace BankManagementSystem.DataAccess.Repositories
{
    /// <summary>
    /// مستودع بيانات الحسابات
    /// </summary>
    public class AccountRepository
    {
        /// <summary>
        /// إضافة حساب جديد
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>معرف الحساب الجديد</returns>
        public int AddAccount(Account account)
        {
            string query = @"
                INSERT INTO Accounts (AccountNumber, CustomerId, AccountType, Balance, MinimumBalance, InterestRate, BranchId, IsActive, OpenDate, Notes)
                VALUES (@AccountNumber, @CustomerId, @AccountType, @Balance, @MinimumBalance, @InterestRate, @BranchId, @IsActive, @OpenDate, @Notes);
                SELECT SCOPE_IDENTITY();";
            
            var parameters = new[]
            {
                new SqlParameter("@AccountNumber", account.AccountNumber),
                new SqlParameter("@CustomerId", account.CustomerId),
                new SqlParameter("@AccountType", (int)account.AccountType),
                new SqlParameter("@Balance", account.Balance),
                new SqlParameter("@MinimumBalance", account.MinimumBalance),
                new SqlParameter("@InterestRate", account.InterestRate ?? (object)DBNull.Value),
                new SqlParameter("@BranchId", account.BranchId),
                new SqlParameter("@IsActive", account.IsActive),
                new SqlParameter("@OpenDate", account.OpenDate),
                new SqlParameter("@Notes", account.Notes ?? (object)DBNull.Value)
            };
            
            var result = DatabaseConnection.ExecuteScalar(query, parameters);
            return Convert.ToInt32(result);
        }
        
        /// <summary>
        /// البحث عن حساب بواسطة رقم الحساب
        /// </summary>
        /// <param name="accountNumber">رقم الحساب</param>
        /// <returns>بيانات الحساب أو null</returns>
        public Account GetAccountByNumber(string accountNumber)
        {
            string query = @"
                SELECT AccountId, AccountNumber, CustomerId, AccountType, Balance, MinimumBalance, InterestRate, BranchId, IsActive, OpenDate, CloseDate, LastTransactionDate, Notes
                FROM Accounts 
                WHERE AccountNumber = @AccountNumber";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@AccountNumber", accountNumber);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Account
                            {
                                AccountId = reader.GetInt32("AccountId"),
                                AccountNumber = reader.GetString("AccountNumber"),
                                CustomerId = reader.GetInt32("CustomerId"),
                                AccountType = (AccountType)reader.GetInt32("AccountType"),
                                Balance = reader.GetDecimal("Balance"),
                                MinimumBalance = reader.GetDecimal("MinimumBalance"),
                                InterestRate = reader.IsDBNull("InterestRate") ? null : reader.GetDecimal("InterestRate"),
                                BranchId = reader.GetInt32("BranchId"),
                                IsActive = reader.GetBoolean("IsActive"),
                                OpenDate = reader.GetDateTime("OpenDate"),
                                CloseDate = reader.IsDBNull("CloseDate") ? null : reader.GetDateTime("CloseDate"),
                                LastTransactionDate = reader.IsDBNull("LastTransactionDate") ? null : reader.GetDateTime("LastTransactionDate"),
                                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes")
                            };
                        }
                    }
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// الحصول على حسابات العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>قائمة حسابات العميل</returns>
        public List<Account> GetAccountsByCustomerId(int customerId)
        {
            var accounts = new List<Account>();
            string query = @"
                SELECT AccountId, AccountNumber, CustomerId, AccountType, Balance, MinimumBalance, InterestRate, BranchId, IsActive, OpenDate, CloseDate, LastTransactionDate, Notes
                FROM Accounts 
                WHERE CustomerId = @CustomerId
                ORDER BY OpenDate DESC";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@CustomerId", customerId);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            accounts.Add(new Account
                            {
                                AccountId = reader.GetInt32("AccountId"),
                                AccountNumber = reader.GetString("AccountNumber"),
                                CustomerId = reader.GetInt32("CustomerId"),
                                AccountType = (AccountType)reader.GetInt32("AccountType"),
                                Balance = reader.GetDecimal("Balance"),
                                MinimumBalance = reader.GetDecimal("MinimumBalance"),
                                InterestRate = reader.IsDBNull("InterestRate") ? null : reader.GetDecimal("InterestRate"),
                                BranchId = reader.GetInt32("BranchId"),
                                IsActive = reader.GetBoolean("IsActive"),
                                OpenDate = reader.GetDateTime("OpenDate"),
                                CloseDate = reader.IsDBNull("CloseDate") ? null : reader.GetDateTime("CloseDate"),
                                LastTransactionDate = reader.IsDBNull("LastTransactionDate") ? null : reader.GetDateTime("LastTransactionDate"),
                                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes")
                            });
                        }
                    }
                }
            }
            
            return accounts;
        }
        
        /// <summary>
        /// تحديث رصيد الحساب
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="newBalance">الرصيد الجديد</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateAccountBalance(int accountId, decimal newBalance)
        {
            string query = @"
                UPDATE Accounts 
                SET Balance = @Balance, LastTransactionDate = @LastTransactionDate
                WHERE AccountId = @AccountId";
            
            var parameters = new[]
            {
                new SqlParameter("@Balance", newBalance),
                new SqlParameter("@LastTransactionDate", DateTime.Now),
                new SqlParameter("@AccountId", accountId)
            };
            
            int rowsAffected = DatabaseConnection.ExecuteNonQuery(query, parameters);
            return rowsAffected > 0;
        }
        
        /// <summary>
        /// توليد رقم حساب جديد
        /// </summary>
        /// <param name="branchId">معرف الفرع</param>
        /// <param name="accountType">نوع الحساب</param>
        /// <returns>رقم الحساب الجديد</returns>
        public string GenerateAccountNumber(int branchId, AccountType accountType)
        {
            string prefix = accountType == AccountType.Current ? "CUR" : "SAV";
            string branchCode = branchId.ToString("D3");
            
            // الحصول على آخر رقم تسلسلي للفرع ونوع الحساب
            string query = @"
                SELECT COUNT(*) + 1 
                FROM Accounts 
                WHERE BranchId = @BranchId AND AccountType = @AccountType";
            
            var parameters = new[]
            {
                new SqlParameter("@BranchId", branchId),
                new SqlParameter("@AccountType", (int)accountType)
            };
            
            var result = DatabaseConnection.ExecuteScalar(query, parameters);
            int sequenceNumber = Convert.ToInt32(result);
            
            return $"{prefix}{branchCode}{sequenceNumber:D6}";
        }
    }
}

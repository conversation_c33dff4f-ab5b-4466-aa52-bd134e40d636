using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using BankManagementSystem.Models.Entities;

namespace BankManagementSystem.DataAccess.Repositories
{
    /// <summary>
    /// مستودع بيانات العملاء
    /// </summary>
    public class CustomerRepository
    {
        /// <summary>
        /// إضافة عميل جديد
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <returns>معرف العميل الجديد</returns>
        public int AddCustomer(Customer customer)
        {
            string query = @"
                INSERT INTO Customers (UserId, NationalId, FirstName, LastName, DateOfBirth, Gender, Address, City, PostalCode, PhoneNumber, Email, Occupation, MonthlyIncome, BranchId, IsActive, RegistrationDate)
                VALUES (@UserId, @NationalId, @FirstName, @LastName, @DateOfBirth, @Gender, @Address, @City, @PostalCode, @PhoneNumber, @Email, @Occupation, @MonthlyIncome, @BranchId, @IsActive, @RegistrationDate);
                SELECT SCOPE_IDENTITY();";
            
            var parameters = new[]
            {
                new SqlParameter("@UserId", customer.UserId),
                new SqlParameter("@NationalId", customer.NationalId),
                new SqlParameter("@FirstName", customer.FirstName),
                new SqlParameter("@LastName", customer.LastName),
                new SqlParameter("@DateOfBirth", customer.DateOfBirth),
                new SqlParameter("@Gender", customer.Gender ?? (object)DBNull.Value),
                new SqlParameter("@Address", customer.Address),
                new SqlParameter("@City", customer.City),
                new SqlParameter("@PostalCode", customer.PostalCode ?? (object)DBNull.Value),
                new SqlParameter("@PhoneNumber", customer.PhoneNumber ?? (object)DBNull.Value),
                new SqlParameter("@Email", customer.Email ?? (object)DBNull.Value),
                new SqlParameter("@Occupation", customer.Occupation ?? (object)DBNull.Value),
                new SqlParameter("@MonthlyIncome", customer.MonthlyIncome ?? (object)DBNull.Value),
                new SqlParameter("@BranchId", customer.BranchId),
                new SqlParameter("@IsActive", customer.IsActive),
                new SqlParameter("@RegistrationDate", customer.RegistrationDate)
            };
            
            var result = DatabaseConnection.ExecuteScalar(query, parameters);
            return Convert.ToInt32(result);
        }
        
        /// <summary>
        /// البحث عن عميل بواسطة معرف المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>بيانات العميل أو null</returns>
        public Customer GetCustomerByUserId(int userId)
        {
            string query = @"
                SELECT CustomerId, UserId, NationalId, FirstName, LastName, DateOfBirth, Gender, Address, City, PostalCode, PhoneNumber, Email, Occupation, MonthlyIncome, BranchId, IsActive, RegistrationDate, LastUpdated
                FROM Customers 
                WHERE UserId = @UserId AND IsActive = 1";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@UserId", userId);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Customer
                            {
                                CustomerId = reader.GetInt32("CustomerId"),
                                UserId = reader.GetInt32("UserId"),
                                NationalId = reader.GetString("NationalId"),
                                FirstName = reader.GetString("FirstName"),
                                LastName = reader.GetString("LastName"),
                                DateOfBirth = reader.GetDateTime("DateOfBirth"),
                                Gender = reader.IsDBNull("Gender") ? null : reader.GetString("Gender"),
                                Address = reader.GetString("Address"),
                                City = reader.GetString("City"),
                                PostalCode = reader.IsDBNull("PostalCode") ? null : reader.GetString("PostalCode"),
                                PhoneNumber = reader.IsDBNull("PhoneNumber") ? null : reader.GetString("PhoneNumber"),
                                Email = reader.IsDBNull("Email") ? null : reader.GetString("Email"),
                                Occupation = reader.IsDBNull("Occupation") ? null : reader.GetString("Occupation"),
                                MonthlyIncome = reader.IsDBNull("MonthlyIncome") ? null : reader.GetDecimal("MonthlyIncome"),
                                BranchId = reader.GetInt32("BranchId"),
                                IsActive = reader.GetBoolean("IsActive"),
                                RegistrationDate = reader.GetDateTime("RegistrationDate"),
                                LastUpdated = reader.IsDBNull("LastUpdated") ? null : reader.GetDateTime("LastUpdated")
                            };
                        }
                    }
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// البحث عن عميل بواسطة رقم الهوية
        /// </summary>
        /// <param name="nationalId">رقم الهوية</param>
        /// <returns>بيانات العميل أو null</returns>
        public Customer GetCustomerByNationalId(string nationalId)
        {
            string query = @"
                SELECT CustomerId, UserId, NationalId, FirstName, LastName, DateOfBirth, Gender, Address, City, PostalCode, PhoneNumber, Email, Occupation, MonthlyIncome, BranchId, IsActive, RegistrationDate, LastUpdated
                FROM Customers 
                WHERE NationalId = @NationalId";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@NationalId", nationalId);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Customer
                            {
                                CustomerId = reader.GetInt32("CustomerId"),
                                UserId = reader.GetInt32("UserId"),
                                NationalId = reader.GetString("NationalId"),
                                FirstName = reader.GetString("FirstName"),
                                LastName = reader.GetString("LastName"),
                                DateOfBirth = reader.GetDateTime("DateOfBirth"),
                                Gender = reader.IsDBNull("Gender") ? null : reader.GetString("Gender"),
                                Address = reader.GetString("Address"),
                                City = reader.GetString("City"),
                                PostalCode = reader.IsDBNull("PostalCode") ? null : reader.GetString("PostalCode"),
                                PhoneNumber = reader.IsDBNull("PhoneNumber") ? null : reader.GetString("PhoneNumber"),
                                Email = reader.IsDBNull("Email") ? null : reader.GetString("Email"),
                                Occupation = reader.IsDBNull("Occupation") ? null : reader.GetString("Occupation"),
                                MonthlyIncome = reader.IsDBNull("MonthlyIncome") ? null : reader.GetDecimal("MonthlyIncome"),
                                BranchId = reader.GetInt32("BranchId"),
                                IsActive = reader.GetBoolean("IsActive"),
                                RegistrationDate = reader.GetDateTime("RegistrationDate"),
                                LastUpdated = reader.IsDBNull("LastUpdated") ? null : reader.GetDateTime("LastUpdated")
                            };
                        }
                    }
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// تحديث بيانات العميل
        /// </summary>
        /// <param name="customer">بيانات العميل المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateCustomer(Customer customer)
        {
            string query = @"
                UPDATE Customers 
                SET FirstName = @FirstName, LastName = @LastName, DateOfBirth = @DateOfBirth, Gender = @Gender, 
                    Address = @Address, City = @City, PostalCode = @PostalCode, PhoneNumber = @PhoneNumber, 
                    Email = @Email, Occupation = @Occupation, MonthlyIncome = @MonthlyIncome, LastUpdated = @LastUpdated
                WHERE CustomerId = @CustomerId";
            
            var parameters = new[]
            {
                new SqlParameter("@FirstName", customer.FirstName),
                new SqlParameter("@LastName", customer.LastName),
                new SqlParameter("@DateOfBirth", customer.DateOfBirth),
                new SqlParameter("@Gender", customer.Gender ?? (object)DBNull.Value),
                new SqlParameter("@Address", customer.Address),
                new SqlParameter("@City", customer.City),
                new SqlParameter("@PostalCode", customer.PostalCode ?? (object)DBNull.Value),
                new SqlParameter("@PhoneNumber", customer.PhoneNumber ?? (object)DBNull.Value),
                new SqlParameter("@Email", customer.Email ?? (object)DBNull.Value),
                new SqlParameter("@Occupation", customer.Occupation ?? (object)DBNull.Value),
                new SqlParameter("@MonthlyIncome", customer.MonthlyIncome ?? (object)DBNull.Value),
                new SqlParameter("@LastUpdated", DateTime.Now),
                new SqlParameter("@CustomerId", customer.CustomerId)
            };
            
            int rowsAffected = DatabaseConnection.ExecuteNonQuery(query, parameters);
            return rowsAffected > 0;
        }
    }
}

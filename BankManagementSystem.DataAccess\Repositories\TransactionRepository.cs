using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using BankManagementSystem.Models.Entities;
using BankManagementSystem.Models.Enums;

namespace BankManagementSystem.DataAccess.Repositories
{
    /// <summary>
    /// مستودع بيانات المعاملات
    /// </summary>
    public class TransactionRepository
    {
        /// <summary>
        /// إضافة معاملة جديدة
        /// </summary>
        /// <param name="transaction">بيانات المعاملة</param>
        /// <returns>معرف المعاملة الجديدة</returns>
        public int AddTransaction(Transaction transaction)
        {
            string query = @"
                INSERT INTO Transactions (TransactionNumber, FromAccountId, ToAccountId, TransactionType, Amount, BalanceBefore, BalanceAfter, Description, ProcessedByUserId, TransactionDate, Status, ReferenceNumber, Notes)
                VALUES (@TransactionNumber, @FromAccountId, @ToAccountId, @TransactionType, @Amount, @BalanceBefore, @BalanceAfter, @Description, @ProcessedByUserId, @TransactionDate, @Status, @ReferenceNumber, @Notes);
                SELECT SCOPE_IDENTITY();";
            
            var parameters = new[]
            {
                new SqlParameter("@TransactionNumber", transaction.TransactionNumber),
                new SqlParameter("@FromAccountId", transaction.FromAccountId),
                new SqlParameter("@ToAccountId", transaction.ToAccountId ?? (object)DBNull.Value),
                new SqlParameter("@TransactionType", (int)transaction.TransactionType),
                new SqlParameter("@Amount", transaction.Amount),
                new SqlParameter("@BalanceBefore", transaction.BalanceBefore),
                new SqlParameter("@BalanceAfter", transaction.BalanceAfter),
                new SqlParameter("@Description", transaction.Description ?? (object)DBNull.Value),
                new SqlParameter("@ProcessedByUserId", transaction.ProcessedByUserId),
                new SqlParameter("@TransactionDate", transaction.TransactionDate),
                new SqlParameter("@Status", transaction.Status ?? "Completed"),
                new SqlParameter("@ReferenceNumber", transaction.ReferenceNumber ?? (object)DBNull.Value),
                new SqlParameter("@Notes", transaction.Notes ?? (object)DBNull.Value)
            };
            
            var result = DatabaseConnection.ExecuteScalar(query, parameters);
            return Convert.ToInt32(result);
        }
        
        /// <summary>
        /// الحصول على معاملات الحساب
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="startDate">تاريخ البداية (اختياري)</param>
        /// <param name="endDate">تاريخ النهاية (اختياري)</param>
        /// <returns>قائمة المعاملات</returns>
        public List<Transaction> GetAccountTransactions(int accountId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var transactions = new List<Transaction>();
            string query = @"
                SELECT TransactionId, TransactionNumber, FromAccountId, ToAccountId, TransactionType, Amount, BalanceBefore, BalanceAfter, Description, ProcessedByUserId, TransactionDate, Status, ReferenceNumber, Notes
                FROM Transactions 
                WHERE (FromAccountId = @AccountId OR ToAccountId = @AccountId)";
            
            if (startDate.HasValue)
                query += " AND TransactionDate >= @StartDate";
            
            if (endDate.HasValue)
                query += " AND TransactionDate <= @EndDate";
            
            query += " ORDER BY TransactionDate DESC";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@AccountId", accountId);
                    
                    if (startDate.HasValue)
                        command.Parameters.AddWithValue("@StartDate", startDate.Value);
                    
                    if (endDate.HasValue)
                        command.Parameters.AddWithValue("@EndDate", endDate.Value);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            transactions.Add(new Transaction
                            {
                                TransactionId = reader.GetInt32("TransactionId"),
                                TransactionNumber = reader.GetString("TransactionNumber"),
                                FromAccountId = reader.GetInt32("FromAccountId"),
                                ToAccountId = reader.IsDBNull("ToAccountId") ? null : reader.GetInt32("ToAccountId"),
                                TransactionType = (TransactionType)reader.GetInt32("TransactionType"),
                                Amount = reader.GetDecimal("Amount"),
                                BalanceBefore = reader.GetDecimal("BalanceBefore"),
                                BalanceAfter = reader.GetDecimal("BalanceAfter"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                ProcessedByUserId = reader.GetInt32("ProcessedByUserId"),
                                TransactionDate = reader.GetDateTime("TransactionDate"),
                                Status = reader.IsDBNull("Status") ? null : reader.GetString("Status"),
                                ReferenceNumber = reader.IsDBNull("ReferenceNumber") ? null : reader.GetString("ReferenceNumber"),
                                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes")
                            });
                        }
                    }
                }
            }
            
            return transactions;
        }
        
        /// <summary>
        /// توليد رقم معاملة جديد
        /// </summary>
        /// <returns>رقم المعاملة الجديد</returns>
        public string GenerateTransactionNumber()
        {
            string prefix = "TXN";
            string datePart = DateTime.Now.ToString("yyyyMMdd");
            
            // الحصول على آخر رقم تسلسلي لليوم
            string query = @"
                SELECT COUNT(*) + 1 
                FROM Transactions 
                WHERE CAST(TransactionDate AS DATE) = CAST(GETDATE() AS DATE)";
            
            var result = DatabaseConnection.ExecuteScalar(query);
            int sequenceNumber = Convert.ToInt32(result);
            
            return $"{prefix}{datePart}{sequenceNumber:D4}";
        }
        
        /// <summary>
        /// البحث عن معاملة بواسطة رقم المعاملة
        /// </summary>
        /// <param name="transactionNumber">رقم المعاملة</param>
        /// <returns>بيانات المعاملة أو null</returns>
        public Transaction GetTransactionByNumber(string transactionNumber)
        {
            string query = @"
                SELECT TransactionId, TransactionNumber, FromAccountId, ToAccountId, TransactionType, Amount, BalanceBefore, BalanceAfter, Description, ProcessedByUserId, TransactionDate, Status, ReferenceNumber, Notes
                FROM Transactions 
                WHERE TransactionNumber = @TransactionNumber";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TransactionNumber", transactionNumber);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Transaction
                            {
                                TransactionId = reader.GetInt32("TransactionId"),
                                TransactionNumber = reader.GetString("TransactionNumber"),
                                FromAccountId = reader.GetInt32("FromAccountId"),
                                ToAccountId = reader.IsDBNull("ToAccountId") ? null : reader.GetInt32("ToAccountId"),
                                TransactionType = (TransactionType)reader.GetInt32("TransactionType"),
                                Amount = reader.GetDecimal("Amount"),
                                BalanceBefore = reader.GetDecimal("BalanceBefore"),
                                BalanceAfter = reader.GetDecimal("BalanceAfter"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                ProcessedByUserId = reader.GetInt32("ProcessedByUserId"),
                                TransactionDate = reader.GetDateTime("TransactionDate"),
                                Status = reader.IsDBNull("Status") ? null : reader.GetString("Status"),
                                ReferenceNumber = reader.IsDBNull("ReferenceNumber") ? null : reader.GetString("ReferenceNumber"),
                                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes")
                            };
                        }
                    }
                }
            }
            
            return null;
        }
    }
}

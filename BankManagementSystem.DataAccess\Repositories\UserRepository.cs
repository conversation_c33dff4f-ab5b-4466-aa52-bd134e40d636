using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using BankManagementSystem.Models.Entities;
using BankManagementSystem.Models.Enums;

namespace BankManagementSystem.DataAccess.Repositories
{
    /// <summary>
    /// مستودع بيانات المستخدمين
    /// </summary>
    public class UserRepository
    {
        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <returns>معرف المستخدم الجديد</returns>
        public int AddUser(User user)
        {
            string query = @"
                INSERT INTO Users (Username, PasswordHash, FullName, Email, PhoneNumber, Role, BranchId, IsActive, CreatedDate)
                VALUES (@Username, @PasswordHash, @FullName, @Email, @PhoneNumber, @Role, @BranchId, @IsActive, @CreatedDate);
                SELECT SCOPE_IDENTITY();";
            
            var parameters = new[]
            {
                new SqlParameter("@Username", user.Username),
                new SqlParameter("@PasswordHash", user.PasswordHash),
                new SqlParameter("@FullName", user.FullName),
                new SqlParameter("@Email", user.Email),
                new SqlParameter("@PhoneNumber", user.PhoneNumber ?? (object)DBNull.Value),
                new SqlParameter("@Role", (int)user.Role),
                new SqlParameter("@BranchId", user.BranchId ?? (object)DBNull.Value),
                new SqlParameter("@IsActive", user.IsActive),
                new SqlParameter("@CreatedDate", user.CreatedDate)
            };
            
            var result = DatabaseConnection.ExecuteScalar(query, parameters);
            return Convert.ToInt32(result);
        }
        
        /// <summary>
        /// البحث عن مستخدم بواسطة اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>بيانات المستخدم أو null</returns>
        public User GetUserByUsername(string username)
        {
            string query = @"
                SELECT UserId, Username, PasswordHash, FullName, Email, PhoneNumber, Role, BranchId, IsActive, CreatedDate, LastLoginDate
                FROM Users 
                WHERE Username = @Username AND IsActive = 1";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new User
                            {
                                UserId = reader.GetInt32("UserId"),
                                Username = reader.GetString("Username"),
                                PasswordHash = reader.GetString("PasswordHash"),
                                FullName = reader.GetString("FullName"),
                                Email = reader.GetString("Email"),
                                PhoneNumber = reader.IsDBNull("PhoneNumber") ? null : reader.GetString("PhoneNumber"),
                                Role = (UserRole)reader.GetInt32("Role"),
                                BranchId = reader.IsDBNull("BranchId") ? null : reader.GetInt32("BranchId"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedDate = reader.GetDateTime("CreatedDate"),
                                LastLoginDate = reader.IsDBNull("LastLoginDate") ? null : reader.GetDateTime("LastLoginDate")
                            };
                        }
                    }
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// تحديث تاريخ آخر تسجيل دخول
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        public void UpdateLastLoginDate(int userId)
        {
            string query = "UPDATE Users SET LastLoginDate = @LastLoginDate WHERE UserId = @UserId";
            
            var parameters = new[]
            {
                new SqlParameter("@LastLoginDate", DateTime.Now),
                new SqlParameter("@UserId", userId)
            };
            
            DatabaseConnection.ExecuteNonQuery(query, parameters);
        }
        
        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        /// <returns>قائمة المستخدمين</returns>
        public List<User> GetAllUsers()
        {
            var users = new List<User>();
            string query = @"
                SELECT UserId, Username, PasswordHash, FullName, Email, PhoneNumber, Role, BranchId, IsActive, CreatedDate, LastLoginDate
                FROM Users 
                ORDER BY FullName";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        users.Add(new User
                        {
                            UserId = reader.GetInt32("UserId"),
                            Username = reader.GetString("Username"),
                            PasswordHash = reader.GetString("PasswordHash"),
                            FullName = reader.GetString("FullName"),
                            Email = reader.GetString("Email"),
                            PhoneNumber = reader.IsDBNull("PhoneNumber") ? null : reader.GetString("PhoneNumber"),
                            Role = (UserRole)reader.GetInt32("Role"),
                            BranchId = reader.IsDBNull("BranchId") ? null : reader.GetInt32("BranchId"),
                            IsActive = reader.GetBoolean("IsActive"),
                            CreatedDate = reader.GetDateTime("CreatedDate"),
                            LastLoginDate = reader.IsDBNull("LastLoginDate") ? null : reader.GetDateTime("LastLoginDate")
                        });
                    }
                }
            }
            
            return users;
        }
    }
}

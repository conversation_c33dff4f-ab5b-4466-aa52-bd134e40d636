using System;
using System.ComponentModel.DataAnnotations;
using BankManagementSystem.Models.Enums;

namespace BankManagementSystem.Models.Entities
{
    /// <summary>
    /// نموذج الحساب المصرفي
    /// </summary>
    public class Account
    {
        /// <summary>
        /// معرف الحساب
        /// </summary>
        public int AccountId { get; set; }
        
        /// <summary>
        /// رقم الحساب
        /// </summary>
        [Required]
        [StringLength(20)]
        public string AccountNumber { get; set; }
        
        /// <summary>
        /// معرف العميل
        /// </summary>
        public int CustomerId { get; set; }
        
        /// <summary>
        /// نوع الحساب
        /// </summary>
        public AccountType AccountType { get; set; }
        
        /// <summary>
        /// الرصيد الحالي
        /// </summary>
        public decimal Balance { get; set; }
        
        /// <summary>
        /// الحد الأدنى للرصيد
        /// </summary>
        public decimal MinimumBalance { get; set; }
        
        /// <summary>
        /// معدل الفائدة (للحسابات التوفير)
        /// </summary>
        public decimal? InterestRate { get; set; }
        
        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }
        
        /// <summary>
        /// حالة النشاط
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// تاريخ فتح الحساب
        /// </summary>
        public DateTime OpenDate { get; set; }
        
        /// <summary>
        /// تاريخ الإغلاق (إن وجد)
        /// </summary>
        public DateTime? CloseDate { get; set; }
        
        /// <summary>
        /// تاريخ آخر معاملة
        /// </summary>
        public DateTime? LastTransactionDate { get; set; }
        
        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }
    }
}

using System;
using System.ComponentModel.DataAnnotations;

namespace BankManagementSystem.Models.Entities
{
    /// <summary>
    /// نموذج الفرع
    /// </summary>
    public class Branch
    {
        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }
        
        /// <summary>
        /// اسم الفرع
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BranchName { get; set; }
        
        /// <summary>
        /// كود الفرع
        /// </summary>
        [Required]
        [StringLength(10)]
        public string BranchCode { get; set; }
        
        /// <summary>
        /// العنوان
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Address { get; set; }
        
        /// <summary>
        /// المدينة
        /// </summary>
        [Required]
        [StringLength(50)]
        public string City { get; set; }
        
        /// <summary>
        /// رقم الهاتف
        /// </summary>
        [StringLength(20)]
        public string PhoneNumber { get; set; }
        
        /// <summary>
        /// معرف مدير الفرع
        /// </summary>
        public int? ManagerId { get; set; }
        
        /// <summary>
        /// حالة النشاط
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }
    }
}

using System;
using System.ComponentModel.DataAnnotations;

namespace BankManagementSystem.Models.Entities
{
    /// <summary>
    /// نموذج العميل
    /// </summary>
    public class Customer
    {
        /// <summary>
        /// معرف العميل
        /// </summary>
        public int CustomerId { get; set; }
        
        /// <summary>
        /// معرف المستخدم المرتبط
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// رقم الهوية الوطنية
        /// </summary>
        [Required]
        [StringLength(20)]
        public string NationalId { get; set; }
        
        /// <summary>
        /// الاسم الأول
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; }
        
        /// <summary>
        /// الاسم الأخير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LastName { get; set; }
        
        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        public DateTime DateOfBirth { get; set; }
        
        /// <summary>
        /// الجنس
        /// </summary>
        [StringLength(10)]
        public string Gender { get; set; }
        
        /// <summary>
        /// العنوان
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Address { get; set; }
        
        /// <summary>
        /// المدينة
        /// </summary>
        [Required]
        [StringLength(50)]
        public string City { get; set; }
        
        /// <summary>
        /// الرمز البريدي
        /// </summary>
        [StringLength(10)]
        public string PostalCode { get; set; }
        
        /// <summary>
        /// رقم الهاتف
        /// </summary>
        [StringLength(20)]
        public string PhoneNumber { get; set; }
        
        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; }
        
        /// <summary>
        /// المهنة
        /// </summary>
        [StringLength(100)]
        public string Occupation { get; set; }
        
        /// <summary>
        /// الدخل الشهري
        /// </summary>
        public decimal? MonthlyIncome { get; set; }
        
        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }
        
        /// <summary>
        /// حالة النشاط
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// تاريخ التسجيل
        /// </summary>
        public DateTime RegistrationDate { get; set; }
        
        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime? LastUpdated { get; set; }
    }
}

using System;
using System.ComponentModel.DataAnnotations;
using BankManagementSystem.Models.Enums;

namespace BankManagementSystem.Models.Entities
{
    /// <summary>
    /// نموذج المعاملة المصرفية
    /// </summary>
    public class Transaction
    {
        /// <summary>
        /// معرف المعاملة
        /// </summary>
        public int TransactionId { get; set; }
        
        /// <summary>
        /// رقم المعاملة
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TransactionNumber { get; set; }
        
        /// <summary>
        /// معرف الحساب المرسل
        /// </summary>
        public int FromAccountId { get; set; }
        
        /// <summary>
        /// معرف الحساب المستقبل (للتحويلات)
        /// </summary>
        public int? ToAccountId { get; set; }
        
        /// <summary>
        /// نوع المعاملة
        /// </summary>
        public TransactionType TransactionType { get; set; }
        
        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// الرصيد قبل المعاملة
        /// </summary>
        public decimal BalanceBefore { get; set; }
        
        /// <summary>
        /// الرصيد بعد المعاملة
        /// </summary>
        public decimal BalanceAfter { get; set; }
        
        /// <summary>
        /// وصف المعاملة
        /// </summary>
        [StringLength(200)]
        public string Description { get; set; }
        
        /// <summary>
        /// معرف المستخدم الذي نفذ المعاملة
        /// </summary>
        public int ProcessedByUserId { get; set; }
        
        /// <summary>
        /// تاريخ ووقت المعاملة
        /// </summary>
        public DateTime TransactionDate { get; set; }
        
        /// <summary>
        /// حالة المعاملة
        /// </summary>
        [StringLength(20)]
        public string Status { get; set; }
        
        /// <summary>
        /// رقم المرجع (للتحويلات الخارجية)
        /// </summary>
        [StringLength(100)]
        public string ReferenceNumber { get; set; }
        
        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }
    }
}

using System;
using System.ComponentModel.DataAnnotations;
using BankManagementSystem.Models.Enums;

namespace BankManagementSystem.Models.Entities
{
    /// <summary>
    /// نموذج المستخدم
    /// </summary>
    public class User
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// اسم المستخدم
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Username { get; set; }
        
        /// <summary>
        /// كلمة المرور المشفرة
        /// </summary>
        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; }
        
        /// <summary>
        /// الاسم الكامل
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FullName { get; set; }
        
        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [Required]
        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; }
        
        /// <summary>
        /// رقم الهاتف
        /// </summary>
        [StringLength(20)]
        public string PhoneNumber { get; set; }
        
        /// <summary>
        /// دور المستخدم
        /// </summary>
        public UserRole Role { get; set; }
        
        /// <summary>
        /// معرف الفرع (للموظفين)
        /// </summary>
        public int? BranchId { get; set; }
        
        /// <summary>
        /// حالة النشاط
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }
        
        /// <summary>
        /// تاريخ آخر تسجيل دخول
        /// </summary>
        public DateTime? LastLoginDate { get; set; }
    }
}

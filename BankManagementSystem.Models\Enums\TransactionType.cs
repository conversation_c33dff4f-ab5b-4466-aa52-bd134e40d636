using System;

namespace BankManagementSystem.Models.Enums
{
    /// <summary>
    /// أنواع المعاملات المصرفية
    /// </summary>
    public enum TransactionType
    {
        /// <summary>
        /// إيداع
        /// </summary>
        Deposit = 1,
        
        /// <summary>
        /// سحب
        /// </summary>
        Withdrawal = 2,
        
        /// <summary>
        /// تحويل
        /// </summary>
        Transfer = 3,
        
        /// <summary>
        /// رسوم
        /// </summary>
        Fee = 4
    }
}

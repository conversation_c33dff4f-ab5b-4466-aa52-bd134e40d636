<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <connectionStrings>
        <add name="BankDB" connectionString="Server=.\SQLEXPRESS;Database=BankManagementDB;Integrated Security=true;" providerName="System.Data.SqlClient" />
    </connectionStrings>
    
    <appSettings>
        <add key="ApplicationName" value="نظام إدارة البنك" />
        <add key="Version" value="1.0.0" />
        <add key="MinimumPasswordLength" value="6" />
        <add key="SessionTimeoutMinutes" value="30" />
        <add key="MaxLoginAttempts" value="3" />
    </appSettings>
    
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
</configuration>

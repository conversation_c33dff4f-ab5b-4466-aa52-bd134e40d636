<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon>bank.ico</ApplicationIcon>
    <AssemblyTitle>نظام إدارة البنك</AssemblyTitle>
    <AssemblyDescription>نظام إدارة بنك متكامل</AssemblyDescription>
    <AssemblyCompany>Bank Management System</AssemblyCompany>
    <AssemblyProduct>Bank Management System</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024</AssemblyCopyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BankManagementSystem.Models\BankManagementSystem.Models.csproj" />
    <ProjectReference Include="..\BankManagementSystem.DataAccess\BankManagementSystem.DataAccess.csproj" />
    <ProjectReference Include="..\BankManagementSystem.Business\BankManagementSystem.Business.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="App.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>

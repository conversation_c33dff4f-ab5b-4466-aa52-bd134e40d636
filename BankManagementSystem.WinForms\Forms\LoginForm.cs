using System;
using System.Drawing;
using System.Windows.Forms;
using BankManagementSystem.Business.Services;
using BankManagementSystem.Models.Enums;

namespace BankManagementSystem.WinForms.Forms
{
    /// <summary>
    /// نموذج تسجيل الدخول
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly AuthenticationService _authService;
        private int _loginAttempts = 0;
        private const int MAX_LOGIN_ATTEMPTS = 3;
        
        public LoginForm()
        {
            InitializeComponent();
            _authService = new AuthenticationService();
            SetupForm();
        }
        
        private void SetupForm()
        {
            // إعداد النموذج
            this.Text = "نظام إدارة البنك - تسجيل الدخول";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // إعداد الخلفية
            this.BackColor = Color.FromArgb(240, 248, 255);
            
            // إنشاء العناصر
            CreateControls();
        }
        
        private void CreateControls()
        {
            // عنوان التطبيق
            var titleLabel = new Label
            {
                Text = "نظام إدارة البنك",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 25, 112),
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(50, 30),
                Size = new Size(350, 40)
            };
            this.Controls.Add(titleLabel);
            
            // أيقونة البنك
            var iconLabel = new Label
            {
                Text = "🏦",
                Font = new Font("Segoe UI Emoji", 24),
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(200, 80),
                Size = new Size(50, 40)
            };
            this.Controls.Add(iconLabel);
            
            // لوحة تسجيل الدخول
            var loginPanel = new Panel
            {
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Location = new Point(50, 140),
                Size = new Size(350, 160)
            };
            this.Controls.Add(loginPanel);
            
            // تسمية اسم المستخدم
            var usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Tahoma", 10),
                Location = new Point(250, 20),
                Size = new Size(80, 20)
            };
            loginPanel.Controls.Add(usernameLabel);
            
            // مربع نص اسم المستخدم
            txtUsername = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(20, 20),
                Size = new Size(220, 25),
                RightToLeft = RightToLeft.No
            };
            loginPanel.Controls.Add(txtUsername);
            
            // تسمية كلمة المرور
            var passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Tahoma", 10),
                Location = new Point(250, 60),
                Size = new Size(80, 20)
            };
            loginPanel.Controls.Add(passwordLabel);
            
            // مربع نص كلمة المرور
            txtPassword = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(20, 60),
                Size = new Size(220, 25),
                UseSystemPasswordChar = true,
                RightToLeft = RightToLeft.No
            };
            loginPanel.Controls.Add(txtPassword);
            
            // زر تسجيل الدخول
            btnLogin = new Button
            {
                Text = "تسجيل الدخول",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(180, 100),
                Size = new Size(120, 35),
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;
            loginPanel.Controls.Add(btnLogin);
            
            // زر الخروج
            var btnExit = new Button
            {
                Text = "خروج",
                Font = new Font("Tahoma", 10),
                BackColor = Color.FromArgb(220, 20, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(50, 100),
                Size = new Size(80, 35),
                Cursor = Cursors.Hand
            };
            btnExit.FlatAppearance.BorderSize = 0;
            btnExit.Click += (s, e) => Application.Exit();
            loginPanel.Controls.Add(btnExit);
            
            // تعيين Enter كزر افتراضي
            this.AcceptButton = btnLogin;
            
            // تركيز على مربع اسم المستخدم
            txtUsername.Focus();
        }
        
        private async void BtnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }
                
                // تعطيل الزر أثناء المعالجة
                btnLogin.Enabled = false;
                btnLogin.Text = "جاري التحقق...";
                
                // محاولة تسجيل الدخول
                bool loginSuccess = _authService.Login(txtUsername.Text.Trim(), txtPassword.Text);
                
                if (loginSuccess)
                {
                    // نجح تسجيل الدخول
                    this.Hide();
                    
                    // فتح النموذج الرئيسي
                    var mainForm = new MainForm(_authService);
                    mainForm.FormClosed += (s, args) => this.Close();
                    mainForm.Show();
                }
                else
                {
                    // فشل تسجيل الدخول
                    _loginAttempts++;
                    
                    if (_loginAttempts >= MAX_LOGIN_ATTEMPTS)
                    {
                        MessageBox.Show("تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. سيتم إغلاق التطبيق.", 
                                      "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        Application.Exit();
                        return;
                    }
                    
                    MessageBox.Show($"اسم المستخدم أو كلمة المرور غير صحيحة.\nالمحاولات المتبقية: {MAX_LOGIN_ATTEMPTS - _loginAttempts}", 
                                  "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    
                    // مسح كلمة المرور
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تسجيل الدخول:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // إعادة تمكين الزر
                btnLogin.Enabled = true;
                btnLogin.Text = "تسجيل الدخول";
            }
        }
        
        // المتغيرات
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
    }
}

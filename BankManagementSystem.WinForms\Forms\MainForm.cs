using System;
using System.Drawing;
using System.Windows.Forms;
using BankManagementSystem.Business.Services;
using BankManagementSystem.Models.Enums;

namespace BankManagementSystem.WinForms.Forms
{
    /// <summary>
    /// النموذج الرئيسي للتطبيق
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly AuthenticationService _authService;
        private Panel _contentPanel;
        private Label _statusLabel;
        private Label _userInfoLabel;
        
        public MainForm(AuthenticationService authService)
        {
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            InitializeComponent();
            SetupForm();
            CreateMenus();
            UpdateUserInterface();
        }
        
        private void SetupForm()
        {
            // إعداد النموذج
            this.Text = "نظام إدارة البنك - الصفحة الرئيسية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.IsMdiContainer = true;
            
            // إعداد الخلفية
            this.BackColor = Color.FromArgb(245, 245, 245);
            
            // إنشاء شريط الحالة
            CreateStatusBar();
            
            // إنشاء لوحة المحتوى
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(10)
            };
            this.Controls.Add(_contentPanel);
            
            // إنشاء صفحة الترحيب
            CreateWelcomePage();
        }
        
        private void CreateStatusBar()
        {
            var statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                RightToLeft = RightToLeft.Yes
            };
            
            _statusLabel = new ToolStripStatusLabel
            {
                Text = "جاهز",
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };
            statusStrip.Items.Add(_statusLabel);
            
            _userInfoLabel = new ToolStripStatusLabel
            {
                Text = $"المستخدم: {_authService.CurrentUser?.FullName} | الدور: {_authService.GetRoleDisplayName(_authService.CurrentUser?.Role ?? UserRole.Customer)}",
                TextAlign = ContentAlignment.MiddleRight
            };
            statusStrip.Items.Add(_userInfoLabel);
            
            var timeLabel = new ToolStripStatusLabel
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                TextAlign = ContentAlignment.MiddleRight
            };
            statusStrip.Items.Add(timeLabel);
            
            // تحديث الوقت كل دقيقة
            var timer = new Timer { Interval = 60000 };
            timer.Tick += (s, e) => timeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
            timer.Start();
            
            this.Controls.Add(statusStrip);
        }
        
        private void CreateMenus()
        {
            var menuStrip = new MenuStrip
            {
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10),
                RightToLeft = RightToLeft.Yes
            };
            
            // قائمة الحسابات
            var accountsMenu = new ToolStripMenuItem("الحسابات");
            accountsMenu.DropDownItems.Add(CreateMenuItem("فتح حساب جديد", "🆕", OpenNewAccountForm));
            accountsMenu.DropDownItems.Add(CreateMenuItem("عرض الحسابات", "👁", ViewAccountsForm));
            accountsMenu.DropDownItems.Add(CreateMenuItem("البحث عن حساب", "🔍", SearchAccountForm));
            menuStrip.Items.Add(accountsMenu);
            
            // قائمة المعاملات
            var transactionsMenu = new ToolStripMenuItem("المعاملات");
            transactionsMenu.DropDownItems.Add(CreateMenuItem("إيداع", "💰", DepositForm));
            transactionsMenu.DropDownItems.Add(CreateMenuItem("سحب", "💸", WithdrawalForm));
            transactionsMenu.DropDownItems.Add(CreateMenuItem("تحويل", "🔄", TransferForm));
            transactionsMenu.DropDownItems.Add(CreateMenuItem("سجل المعاملات", "📋", TransactionHistoryForm));
            menuStrip.Items.Add(transactionsMenu);
            
            // قائمة العملاء (للموظفين فقط)
            if (_authService.HasPermission(UserRole.BankEmployee))
            {
                var customersMenu = new ToolStripMenuItem("العملاء");
                customersMenu.DropDownItems.Add(CreateMenuItem("تسجيل عميل جديد", "👤", RegisterCustomerForm));
                customersMenu.DropDownItems.Add(CreateMenuItem("عرض العملاء", "👥", ViewCustomersForm));
                customersMenu.DropDownItems.Add(CreateMenuItem("تحديث بيانات عميل", "✏", UpdateCustomerForm));
                menuStrip.Items.Add(customersMenu);
            }
            
            // قائمة التقارير (للمديرين فقط)
            if (_authService.HasPermission(UserRole.BranchManager))
            {
                var reportsMenu = new ToolStripMenuItem("التقارير");
                reportsMenu.DropDownItems.Add(CreateMenuItem("تقرير يومي", "📊", DailyReportForm));
                reportsMenu.DropDownItems.Add(CreateMenuItem("تقرير شهري", "📈", MonthlyReportForm));
                reportsMenu.DropDownItems.Add(CreateMenuItem("تقرير العملاء", "👥", CustomerReportForm));
                menuStrip.Items.Add(reportsMenu);
            }
            
            // قائمة الإدارة (لرئيس البنك فقط)
            if (_authService.HasPermission(UserRole.BankPresident))
            {
                var adminMenu = new ToolStripMenuItem("الإدارة");
                adminMenu.DropDownItems.Add(CreateMenuItem("إدارة المستخدمين", "👨‍💼", ManageUsersForm));
                adminMenu.DropDownItems.Add(CreateMenuItem("إدارة الفروع", "🏢", ManageBranchesForm));
                adminMenu.DropDownItems.Add(CreateMenuItem("إعدادات النظام", "⚙", SystemSettingsForm));
                menuStrip.Items.Add(adminMenu);
            }
            
            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add(CreateMenuItem("حول البرنامج", "ℹ", AboutForm));
            helpMenu.DropDownItems.Add(CreateMenuItem("دليل المستخدم", "📖", UserGuideForm));
            menuStrip.Items.Add(helpMenu);
            
            // قائمة النظام
            var systemMenu = new ToolStripMenuItem("النظام");
            systemMenu.DropDownItems.Add(CreateMenuItem("تغيير كلمة المرور", "🔐", ChangePasswordForm));
            systemMenu.DropDownItems.Add(new ToolStripSeparator());
            systemMenu.DropDownItems.Add(CreateMenuItem("تسجيل الخروج", "🚪", Logout));
            systemMenu.DropDownItems.Add(CreateMenuItem("خروج", "❌", ExitApplication));
            menuStrip.Items.Add(systemMenu);
            
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }
        
        private ToolStripMenuItem CreateMenuItem(string text, string icon, Action action)
        {
            var menuItem = new ToolStripMenuItem($"{icon} {text}")
            {
                ForeColor = Color.White,
                BackColor = Color.FromArgb(70, 130, 180)
            };
            menuItem.Click += (s, e) => action?.Invoke();
            return menuItem;
        }
        
        private void CreateWelcomePage()
        {
            _contentPanel.Controls.Clear();
            
            var welcomeLabel = new Label
            {
                Text = $"مرحباً بك، {_authService.CurrentUser?.FullName}",
                Font = new Font("Tahoma", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 25, 112),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 80
            };
            _contentPanel.Controls.Add(welcomeLabel);
            
            var roleLabel = new Label
            {
                Text = $"دورك في النظام: {_authService.GetRoleDisplayName(_authService.CurrentUser?.Role ?? UserRole.Customer)}",
                Font = new Font("Tahoma", 14),
                ForeColor = Color.FromArgb(70, 130, 180),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };
            _contentPanel.Controls.Add(roleLabel);
            
            var instructionLabel = new Label
            {
                Text = "استخدم القوائم أعلاه للوصول إلى الوظائف المختلفة للنظام",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };
            _contentPanel.Controls.Add(instructionLabel);
        }
        
        private void UpdateUserInterface()
        {
            // تحديث واجهة المستخدم بناءً على دور المستخدم
            _statusLabel.Text = "جاهز";
        }
        
        // أحداث القوائم (سيتم تنفيذها لاحقاً)
        private void OpenNewAccountForm() => ShowNotImplemented("فتح حساب جديد");
        private void ViewAccountsForm() => ShowNotImplemented("عرض الحسابات");
        private void SearchAccountForm() => ShowNotImplemented("البحث عن حساب");
        private void DepositForm() => ShowNotImplemented("إيداع");
        private void WithdrawalForm() => ShowNotImplemented("سحب");
        private void TransferForm() => ShowNotImplemented("تحويل");
        private void TransactionHistoryForm() => ShowNotImplemented("سجل المعاملات");
        private void RegisterCustomerForm() => ShowNotImplemented("تسجيل عميل جديد");
        private void ViewCustomersForm() => ShowNotImplemented("عرض العملاء");
        private void UpdateCustomerForm() => ShowNotImplemented("تحديث بيانات عميل");
        private void DailyReportForm() => ShowNotImplemented("تقرير يومي");
        private void MonthlyReportForm() => ShowNotImplemented("تقرير شهري");
        private void CustomerReportForm() => ShowNotImplemented("تقرير العملاء");
        private void ManageUsersForm() => ShowNotImplemented("إدارة المستخدمين");
        private void ManageBranchesForm() => ShowNotImplemented("إدارة الفروع");
        private void SystemSettingsForm() => ShowNotImplemented("إعدادات النظام");
        private void AboutForm() => ShowNotImplemented("حول البرنامج");
        private void UserGuideForm() => ShowNotImplemented("دليل المستخدم");
        private void ChangePasswordForm() => ShowNotImplemented("تغيير كلمة المرور");
        
        private void ShowNotImplemented(string feature)
        {
            MessageBox.Show($"ميزة '{feature}' قيد التطوير وستكون متاحة قريباً.", "قيد التطوير", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void Logout()
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", 
                                       MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                _authService.Logout();
                this.Hide();
                var loginForm = new LoginForm();
                loginForm.Show();
                this.Close();
            }
        }
        
        private void ExitApplication()
        {
            var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد", 
                                       MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }
    }
}

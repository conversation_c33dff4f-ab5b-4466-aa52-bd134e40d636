using System;
using System.Windows.Forms;
using BankManagementSystem.WinForms.Forms;
using BankManagementSystem.DataAccess;

namespace BankManagementSystem.WinForms
{
    /// <summary>
    /// نقطة دخول التطبيق الرئيسية
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// نقطة دخول التطبيق الرئيسية
        /// </summary>
        [STAThread]
        static void Main()
        {
            // تمكين الأنماط البصرية
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // تعيين اللغة العربية كافتراضية
            System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("ar-SA");
            System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("ar-SA");

            try
            {
                // تهيئة قاعدة البيانات
                if (!DatabaseInitializer.InitializeDatabase())
                {
                    MessageBox.Show("فشل في تهيئة قاعدة البيانات. سيتم إغلاق التطبيق.",
                                  "خطأ قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // التأكد من وجود البيانات الأولية
                DatabaseInitializer.EnsureInitialData();

                // بدء التطبيق بنموذج تسجيل الدخول
                Application.Run(new LoginForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في بدء التطبيق:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

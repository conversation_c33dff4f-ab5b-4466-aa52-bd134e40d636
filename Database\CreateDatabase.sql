-- إنشاء قاعدة بيانات نظام إدارة البنك
-- Bank Management System Database Creation Script

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'BankManagementDB')
BEGIN
    CREATE DATABASE BankManagementDB;
END
GO

USE BankManagementDB;
GO

-- إنشاء جدول الفروع
CREATE TABLE Branches (
    BranchId INT IDENTITY(1,1) PRIMARY KEY,
    BranchName NVARCHAR(100) NOT NULL,
    BranchCode NVARCHAR(10) NOT NULL UNIQUE,
    Address NVARCHAR(200) NOT NULL,
    City NVARCHAR(50) NOT NULL,
    PhoneNumber NVARCHAR(20),
    ManagerId INT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- إن<PERSON>اء جدول المستخدمين
CREATE TABLE Users (
    UserId INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100) NOT NULL,
    PhoneNumber NVARCHAR(20),
    Role INT NOT NULL, -- 1=Customer, 2=BankEmployee, 3=BranchManager, 4=BankPresident
    BranchId INT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    LastLoginDate DATETIME NULL,
    FOREIGN KEY (BranchId) REFERENCES Branches(BranchId)
);

-- إنشاء جدول العملاء
CREATE TABLE Customers (
    CustomerId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    NationalId NVARCHAR(20) NOT NULL UNIQUE,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    DateOfBirth DATE NOT NULL,
    Gender NVARCHAR(10),
    Address NVARCHAR(200) NOT NULL,
    City NVARCHAR(50) NOT NULL,
    PostalCode NVARCHAR(10),
    PhoneNumber NVARCHAR(20),
    Email NVARCHAR(100),
    Occupation NVARCHAR(100),
    MonthlyIncome DECIMAL(18,2),
    BranchId INT NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    RegistrationDate DATETIME NOT NULL DEFAULT GETDATE(),
    LastUpdated DATETIME NULL,
    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    FOREIGN KEY (BranchId) REFERENCES Branches(BranchId)
);

-- إنشاء جدول الحسابات
CREATE TABLE Accounts (
    AccountId INT IDENTITY(1,1) PRIMARY KEY,
    AccountNumber NVARCHAR(20) NOT NULL UNIQUE,
    CustomerId INT NOT NULL,
    AccountType INT NOT NULL, -- 1=Current, 2=Savings
    Balance DECIMAL(18,2) NOT NULL DEFAULT 0,
    MinimumBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
    InterestRate DECIMAL(5,4) NULL,
    BranchId INT NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    OpenDate DATETIME NOT NULL DEFAULT GETDATE(),
    CloseDate DATETIME NULL,
    LastTransactionDate DATETIME NULL,
    Notes NVARCHAR(500),
    FOREIGN KEY (CustomerId) REFERENCES Customers(CustomerId),
    FOREIGN KEY (BranchId) REFERENCES Branches(BranchId)
);

-- إنشاء جدول المعاملات
CREATE TABLE Transactions (
    TransactionId INT IDENTITY(1,1) PRIMARY KEY,
    TransactionNumber NVARCHAR(50) NOT NULL UNIQUE,
    FromAccountId INT NOT NULL,
    ToAccountId INT NULL,
    TransactionType INT NOT NULL, -- 1=Deposit, 2=Withdrawal, 3=Transfer, 4=Fee
    Amount DECIMAL(18,2) NOT NULL,
    BalanceBefore DECIMAL(18,2) NOT NULL,
    BalanceAfter DECIMAL(18,2) NOT NULL,
    Description NVARCHAR(200),
    ProcessedByUserId INT NOT NULL,
    TransactionDate DATETIME NOT NULL DEFAULT GETDATE(),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Completed',
    ReferenceNumber NVARCHAR(100),
    Notes NVARCHAR(500),
    FOREIGN KEY (FromAccountId) REFERENCES Accounts(AccountId),
    FOREIGN KEY (ToAccountId) REFERENCES Accounts(AccountId),
    FOREIGN KEY (ProcessedByUserId) REFERENCES Users(UserId)
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IX_Users_Username ON Users(Username);
CREATE INDEX IX_Users_Role ON Users(Role);
CREATE INDEX IX_Customers_NationalId ON Customers(NationalId);
CREATE INDEX IX_Accounts_AccountNumber ON Accounts(AccountNumber);
CREATE INDEX IX_Accounts_CustomerId ON Accounts(CustomerId);
CREATE INDEX IX_Transactions_FromAccountId ON Transactions(FromAccountId);
CREATE INDEX IX_Transactions_ToAccountId ON Transactions(ToAccountId);
CREATE INDEX IX_Transactions_TransactionDate ON Transactions(TransactionDate);
CREATE INDEX IX_Transactions_TransactionNumber ON Transactions(TransactionNumber);

-- إدراج بيانات أولية

-- إدراج فرع رئيسي
INSERT INTO Branches (BranchName, BranchCode, Address, City, PhoneNumber)
VALUES (N'الفرع الرئيسي', 'MAIN001', N'شارع الملك فهد، الرياض', N'الرياض', '011-1234567');

-- إدراج مستخدم رئيس البنك الافتراضي
-- كلمة المرور: admin123 (مشفرة بـ SHA256)
INSERT INTO Users (Username, PasswordHash, FullName, Email, Role, BranchId)
VALUES ('admin', 'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', 
        N'مدير النظام', '<EMAIL>', 4, 1);

-- إدراج مستخدم موظف تجريبي
-- كلمة المرور: emp123 (مشفرة بـ SHA256)
INSERT INTO Users (Username, PasswordHash, FullName, Email, Role, BranchId)
VALUES ('employee1', '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090', 
        N'أحمد محمد', '<EMAIL>', 2, 1);

-- إدراج مستخدم عميل تجريبي
-- كلمة المرور: cust123 (مشفرة بـ SHA256)
INSERT INTO Users (Username, PasswordHash, FullName, Email, Role, BranchId)
VALUES ('customer1', 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3',
        N'سارة أحمد', '<EMAIL>', 1, 1);

-- إدراج بيانات العميل التجريبي
INSERT INTO Customers (UserId, NationalId, FirstName, LastName, DateOfBirth, Gender, Address, City, BranchId)
VALUES (3, '**********', N'سارة', N'أحمد', '1990-01-01', N'أنثى', N'حي النخيل، الرياض', N'الرياض', 1);

-- إنشاء حساب تجريبي للعميل
INSERT INTO Accounts (AccountNumber, CustomerId, AccountType, Balance, MinimumBalance, BranchId)
VALUES ('CUR001000001', 1, 1, 5000.00, 1000.00, 1);

INSERT INTO Accounts (AccountNumber, CustomerId, AccountType, Balance, MinimumBalance, InterestRate, BranchId)
VALUES ('SAV001000001', 1, 2, 10000.00, 500.00, 0.02, 1);

PRINT 'تم إنشاء قاعدة البيانات بنجاح!';
PRINT 'بيانات تسجيل الدخول الافتراضية:';
PRINT 'رئيس البنك - اسم المستخدم: admin، كلمة المرور: admin123';
PRINT 'الموظف - اسم المستخدم: employee1، كلمة المرور: emp123';
PRINT 'العميل - اسم المستخدم: customer1، كلمة المرور: cust123';
GO

# دليل التثبيت والتشغيل
## Installation and Setup Guide

## 📋 المتطلبات الأساسية

### 1. متطلبات النظام
- **نظام التشغيل**: Windows 7 أو أحدث
- **الذاكرة**: 2 GB RAM كحد أدنى (4 GB مستحسن)
- **مساحة القرص**: 500 MB مساحة فارغة
- **الدقة**: 1024x768 كحد أدنى

### 2. البرامج المطلوبة
- **.NET Framework 4.8** أو أحدث
- **SQL Server 2012** أو أحدث (أو SQL Server Express - مجاني)
- **Visual Studio 2019** أو أحدث (للتطوير فقط)

## 🔧 خطوات التثبيت

### الخطوة 1: تثبيت SQL Server

#### خيار أ: SQL Server Express (مجاني - مستحسن للاختبار)
1. تحميل SQL Server Express من [موقع مايكروسوفت](https://www.microsoft.com/en-us/sql-server/sql-server-downloads)
2. تشغيل ملف التثبيت
3. اختيار "Basic" installation
4. اتباع التعليمات حتى اكتمال التثبيت
5. تسجيل اسم الخادم (عادة `.\SQLEXPRESS`)

#### خيار ب: SQL Server Developer Edition (مجاني - للتطوير)
1. تحميل SQL Server Developer من موقع مايكروسوفت
2. تشغيل ملف التثبيت
3. اختيار "Custom" installation
4. تحديد المكونات المطلوبة
5. إكمال التثبيت

### الخطوة 2: تثبيت .NET Framework
1. تحميل .NET Framework 4.8 من موقع مايكروسوفت
2. تشغيل ملف التثبيت
3. إعادة تشغيل الكمبيوتر إذا طُلب ذلك

### الخطوة 3: إعداد المشروع

#### للمطورين (مع Visual Studio):
```bash
# 1. استنساخ المشروع
git clone [repository-url]
cd BankManagementSystem

# 2. فتح الحل في Visual Studio
# فتح ملف BankManagementSystem.sln

# 3. استعادة الحزم
# في Package Manager Console:
Update-Package -reinstall

# 4. بناء الحل
# Build -> Build Solution (Ctrl+Shift+B)
```

#### للمستخدمين النهائيين:
1. تحميل ملفات التطبيق المبنية
2. استخراج الملفات إلى مجلد مناسب
3. التأكد من وجود جميع الملفات المطلوبة

## 🗄️ إعداد قاعدة البيانات

### الطريقة الأولى: التهيئة التلقائية (مستحسنة)
1. تشغيل التطبيق للمرة الأولى
2. سيقوم التطبيق تلقائياً بإنشاء قاعدة البيانات
3. إذا فشلت العملية، اتبع الطريقة اليدوية

### الطريقة الثانية: الإعداد اليدوي
1. فتح SQL Server Management Studio (SSMS)
2. الاتصال بخادم SQL Server
3. فتح ملف `Database/CreateDatabase.sql`
4. تنفيذ السكريبت (F5)
5. التأكد من إنشاء قاعدة البيانات `BankManagementDB`

## ⚙️ تكوين الاتصال

### تحديث سلسلة الاتصال
قم بتحرير ملف `App.config` في مجلد التطبيق:

```xml
<connectionStrings>
    <add name="BankDB" 
         connectionString="Server=SERVER_NAME;Database=BankManagementDB;Integrated Security=true;" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

### أمثلة على سلاسل الاتصال:

#### SQL Server Express (محلي):
```xml
<add name="BankDB" 
     connectionString="Server=.\SQLEXPRESS;Database=BankManagementDB;Integrated Security=true;" />
```

#### SQL Server مع مصادقة Windows:
```xml
<add name="BankDB" 
     connectionString="Server=SERVER_NAME;Database=BankManagementDB;Integrated Security=true;" />
```

#### SQL Server مع مصادقة SQL:
```xml
<add name="BankDB" 
     connectionString="Server=SERVER_NAME;Database=BankManagementDB;User Id=USERNAME;Password=PASSWORD;" />
```

## 🚀 تشغيل التطبيق

### للمطورين:
1. فتح Visual Studio
2. تحديد مشروع `BankManagementSystem.WinForms` كمشروع بدء
3. الضغط على F5 أو Debug -> Start Debugging

### للمستخدمين:
1. الانتقال إلى مجلد التطبيق
2. تشغيل `BankManagementSystem.WinForms.exe`

## 🔐 بيانات تسجيل الدخول الافتراضية

| الدور | اسم المستخدم | كلمة المرور | الوصف |
|-------|-------------|------------|--------|
| رئيس البنك | `admin` | `admin123` | صلاحيات كاملة |
| موظف بنك | `employee1` | `emp123` | صلاحيات الموظف |
| عميل | `customer1` | `cust123` | صلاحيات العميل |

**⚠️ تنبيه أمني**: يرجى تغيير كلمات المرور الافتراضية فور تسجيل الدخول الأول.

## 🔧 استكشاف الأخطاء وإصلاحها

### خطأ في الاتصال بقاعدة البيانات
```
System.Data.SqlClient.SqlException: Cannot open database
```
**الحلول**:
1. التأكد من تشغيل خدمة SQL Server
2. التحقق من صحة سلسلة الاتصال
3. التأكد من وجود قاعدة البيانات
4. التحقق من صلاحيات المستخدم

### خطأ في .NET Framework
```
Application requires .NET Framework 4.8
```
**الحل**: تثبيت .NET Framework 4.8 من موقع مايكروسوفت

### خطأ في الملفات المفقودة
```
Could not load file or assembly
```
**الحلول**:
1. التأكد من وجود جميع ملفات DLL
2. إعادة بناء المشروع
3. استعادة حزم NuGet

## 📊 التحقق من التثبيت

### اختبار الاتصال بقاعدة البيانات:
```sql
-- تشغيل في SSMS
USE BankManagementDB;
SELECT COUNT(*) as UserCount FROM Users;
SELECT COUNT(*) as BranchCount FROM Branches;
```

### اختبار تسجيل الدخول:
1. تشغيل التطبيق
2. استخدام بيانات المدير الافتراضية
3. التأكد من ظهور الواجهة الرئيسية

## 🔄 التحديث

### تحديث التطبيق:
1. إيقاف التطبيق
2. نسخ احتياطي من قاعدة البيانات
3. استبدال ملفات التطبيق
4. تشغيل التطبيق للتحقق من التحديثات

### تحديث قاعدة البيانات:
1. نسخ احتياطي من قاعدة البيانات
2. تشغيل سكريبت التحديث
3. اختبار التطبيق

## 📞 الدعم الفني

### في حالة مواجهة مشاكل:
1. التحقق من ملف السجل (Log file)
2. مراجعة رسائل الخطأ
3. التأكد من المتطلبات الأساسية
4. الاتصال بالدعم الفني

### معلومات مفيدة للدعم:
- إصدار Windows
- إصدار .NET Framework
- إصدار SQL Server
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

## ✅ قائمة التحقق النهائية

- [ ] تم تثبيت SQL Server
- [ ] تم تثبيت .NET Framework 4.8
- [ ] تم إنشاء قاعدة البيانات
- [ ] تم تكوين سلسلة الاتصال
- [ ] تم اختبار تسجيل الدخول
- [ ] تم تغيير كلمات المرور الافتراضية
- [ ] تم إنشاء نسخة احتياطية من قاعدة البيانات

---

**ملاحظة**: هذا الدليل مخصص للإصدار الحالي من النظام. قد تختلف الخطوات في الإصدارات المستقبلية.

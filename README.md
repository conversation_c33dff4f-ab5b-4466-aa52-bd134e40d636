# نظام إدارة البنك المتكامل
## Bank Management System

نظام برمجي شامل لإدارة البنك متعدد الأدوار باستخدام لغة C# مع Windows Forms وSQL Server.

## 🎯 الهدف
بناء نظام برمجي شامل لإدارة بنك متعدد الأدوار يعمل بكفاءة على أنظمة Windows 7 فأعلى، ويوفر واجهة استخدام مرنة وسهلة.

## 🏛️ الأدوار الرئيسية

### 👤 عميل (Customer)
- فتح حساب جديد (جاري أو توفير)
- إيداع وسحب الأموال
- تحويل أموال إلى حسابات أخرى
- عرض رصيد الحساب
- عرض سجل المعاملات
- تحديث البيانات الشخصية

### 👨‍💼 موظف بنك (Bank Employee)
- تسجيل عملاء جدد
- تنفيذ عمليات تحويل/سحب نيابةً عن العملاء
- طباعة كشوف الحساب
- التحقق من عمليات مشبوهة
- عرض بيانات العملاء

### 👨‍💼 مدير فرع (Branch Manager)
- كل صلاحيات الموظف
- إدارة الموظفين في الفرع
- الموافقة على المعاملات الكبيرة
- عرض تقارير مالية خاصة بالفرع
- إنشاء حسابات موظفين

### 👨‍💼 رئيس البنك (Bank President)
- صلاحيات كاملة
- إدارة الفروع والموظفين
- عرض تقارير شاملة لكل الفروع
- تعديل السياسات المالية
- التحكم في إعدادات النظام العامة

## 🏗️ هيكل المشروع

```
BankManagementSystem/
├── BankManagementSystem.sln
├── BankManagementSystem.Models/          # النماذج والكيانات
│   ├── Entities/                         # كيانات قاعدة البيانات
│   └── Enums/                           # التعدادات
├── BankManagementSystem.DataAccess/      # طبقة الوصول للبيانات
│   └── Repositories/                    # مستودعات البيانات
├── BankManagementSystem.Business/        # منطق الأعمال
│   └── Services/                        # الخدمات
├── BankManagementSystem.WinForms/        # واجهة المستخدم
│   └── Forms/                           # النماذج
└── Database/                            # ملفات قاعدة البيانات
    └── CreateDatabase.sql
```

## 🛠️ التقنيات المستخدمة

- **اللغة**: C# (.NET Framework 4.8)
- **واجهة المستخدم**: Windows Forms
- **قاعدة البيانات**: SQL Server / SQL Server Express
- **معمارية**: Layered Architecture (طبقات منفصلة)
- **أمان**: تشفير كلمات المرور بـ SHA256

## 📋 المتطلبات

### متطلبات النظام
- Windows 7 أو أحدث
- .NET Framework 4.8 أو أحدث
- SQL Server 2012 أو أحدث (أو SQL Server Express)
- ذاكرة: 2 GB RAM كحد أدنى
- مساحة القرص: 100 MB

### متطلبات التطوير
- Visual Studio 2019 أو أحدث
- SQL Server Management Studio (اختياري)

## 🚀 التثبيت والتشغيل

### 1. إعداد قاعدة البيانات
```sql
-- تشغيل ملف Database/CreateDatabase.sql في SQL Server
-- أو استخدام SQL Server Management Studio
```

### 2. تكوين سلسلة الاتصال
قم بتحديث ملف `App.config` في مشروع WinForms:
```xml
<connectionStrings>
    <add name="BankDB" 
         connectionString="Server=.\SQLEXPRESS;Database=BankManagementDB;Integrated Security=true;" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 3. بناء وتشغيل المشروع
```bash
# فتح المشروع في Visual Studio
# بناء الحل (Build Solution)
# تشغيل المشروع (F5)
```

## 🔐 بيانات تسجيل الدخول الافتراضية

| الدور | اسم المستخدم | كلمة المرور |
|-------|-------------|------------|
| رئيس البنك | admin | admin123 |
| موظف بنك | employee1 | emp123 |
| عميل | customer1 | cust123 |

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **Users**: المستخدمين
- **Customers**: العملاء
- **Accounts**: الحسابات المصرفية
- **Transactions**: المعاملات المالية
- **Branches**: الفروع

### العلاقات
- مستخدم واحد يمكن أن يكون عميل واحد
- عميل واحد يمكن أن يملك عدة حسابات
- كل معاملة مرتبطة بحساب أو أكثر
- كل فرع يحتوي على عدة موظفين وعملاء

## 🔒 الأمان

- تشفير كلمات المرور باستخدام SHA256
- نظام صلاحيات متدرج حسب الدور
- تسجيل جميع المعاملات مع معرف المستخدم
- حماية من SQL Injection باستخدام Parameterized Queries

## 🎨 واجهة المستخدم

- تصميم باللغة العربية مع دعم RTL
- واجهة سهلة الاستخدام ومتجاوبة
- قوائم ديناميكية حسب دور المستخدم
- رسائل تأكيد وتنبيه واضحة

## 📈 الميزات المستقبلية

- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] إشعارات الأمان والتنبيهات
- [ ] واجهة ويب للعملاء
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع الإلكتروني

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. تنفيذ التغييرات مع الاختبارات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- البريد الإلكتروني: <EMAIL>

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام.

---

**ملاحظة**: هذا النظام مخصص للأغراض التعليمية والتطويرية. للاستخدام في بيئة إنتاجية، يرجى إضافة المزيد من ميزات الأمان والاختبارات.

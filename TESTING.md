# دليل اختبار النظام
## System Testing Guide

## 🧪 أنواع الاختبارات

### 1. اختبار الوحدة (Unit Testing)
- اختبار الخدمات الفردية
- اختبار مستودعات البيانات
- اختبار منطق الأعمال

### 2. اختبار التكامل (Integration Testing)
- اختبار التفاعل بين الطبقات
- اختبار الاتصال بقاعدة البيانات
- اختبار تدفق البيانات

### 3. اختبار واجهة المستخدم (UI Testing)
- اختبار النماذج والقوائم
- اختبار التنقل بين الصفحات
- اختبار الاستجابة للأحداث

## 🔐 اختبار المصادقة والأمان

### اختبار تسجيل الدخول
```
سيناريو: تسجيل دخول صحيح
1. فتح نموذج تسجيل الدخول
2. إدخال اسم مستخدم صحيح: admin
3. إدخال كلمة مرور صحيحة: admin123
4. النقر على "تسجيل الدخول"
النتيجة المتوقعة: فتح الواجهة الرئيسية

سيناريو: تسجيل دخول خاطئ
1. فتح نموذج تسجيل الدخول
2. إدخال اسم مستخدم خاطئ: wronguser
3. إدخال كلمة مرور خاطئة: wrongpass
4. النقر على "تسجيل الدخول"
النتيجة المتوقعة: رسالة خطأ
```

### اختبار الصلاحيات
```
سيناريو: صلاحيات رئيس البنك
1. تسجيل الدخول كرئيس بنك
2. التحقق من ظهور جميع القوائم
النتيجة المتوقعة: ظهور قوائم الإدارة والتقارير

سيناريو: صلاحيات العميل
1. تسجيل الدخول كعميل
2. التحقق من عدم ظهور قوائم الإدارة
النتيجة المتوقعة: ظهور قوائم العميل فقط
```

## 💰 اختبار المعاملات المالية

### اختبار الإيداع
```
البيانات الأولية:
- رقم الحساب: CUR001000001
- الرصيد الحالي: 5000.00

سيناريو: إيداع صحيح
1. فتح نموذج الإيداع
2. إدخال رقم الحساب: CUR001000001
3. إدخال المبلغ: 1000.00
4. إدخال الوصف: إيداع نقدي
5. تأكيد العملية
النتيجة المتوقعة: 
- رصيد جديد: 6000.00
- إنشاء سجل معاملة
- رسالة نجاح العملية
```

### اختبار السحب
```
البيانات الأولية:
- رقم الحساب: CUR001000001
- الرصيد الحالي: 5000.00
- الحد الأدنى: 1000.00

سيناريو: سحب صحيح
1. فتح نموذج السحب
2. إدخال رقم الحساب: CUR001000001
3. إدخال المبلغ: 2000.00
4. تأكيد العملية
النتيجة المتوقعة:
- رصيد جديد: 3000.00
- إنشاء سجل معاملة

سيناريو: سحب يتجاوز الحد المسموح
1. فتح نموذج السحب
2. إدخال رقم الحساب: CUR001000001
3. إدخال المبلغ: 4500.00 (يترك رصيد أقل من الحد الأدنى)
4. تأكيد العملية
النتيجة المتوقعة: رسالة خطأ - رصيد غير كافي
```

### اختبار التحويل
```
البيانات الأولية:
- الحساب المرسل: CUR001000001 (رصيد: 5000.00)
- الحساب المستقبل: SAV001000001 (رصيد: 10000.00)

سيناريو: تحويل صحيح
1. فتح نموذج التحويل
2. إدخال الحساب المرسل: CUR001000001
3. إدخال الحساب المستقبل: SAV001000001
4. إدخال المبلغ: 1500.00
5. تأكيد العملية
النتيجة المتوقعة:
- رصيد الحساب المرسل: 3500.00
- رصيد الحساب المستقبل: 11500.00
- إنشاء سجلين للمعاملة
```

## 👥 اختبار إدارة العملاء

### اختبار تسجيل عميل جديد
```
سيناريو: تسجيل عميل صحيح
1. تسجيل الدخول كموظف
2. فتح نموذج تسجيل عميل جديد
3. إدخال البيانات:
   - الاسم الأول: محمد
   - الاسم الأخير: علي
   - رقم الهوية: 1234567891
   - تاريخ الميلاد: 1985-05-15
   - العنوان: الرياض
4. حفظ البيانات
النتيجة المتوقعة: إنشاء حساب مستخدم وملف عميل
```

### اختبار فتح حساب جديد
```
سيناريو: فتح حساب جاري
1. تحديد العميل
2. اختيار نوع الحساب: جاري
3. إدخال الإيداع الأولي: 2000.00
4. تأكيد العملية
النتيجة المتوقعة:
- إنشاء حساب برقم جديد
- رصيد ابتدائي: 2000.00
- حالة الحساب: نشط
```

## 📊 اختبار التقارير

### اختبار التقرير اليومي
```
سيناريو: تقرير يوم محدد
1. تسجيل الدخول كمدير فرع
2. فتح نموذج التقرير اليومي
3. اختيار التاريخ: اليوم الحالي
4. إنشاء التقرير
النتيجة المتوقعة:
- عرض جميع معاملات اليوم
- إجمالي الإيداعات
- إجمالي السحوبات
- عدد المعاملات
```

## 🗄️ اختبار قاعدة البيانات

### اختبار الاتصال
```sql
-- اختبار الاتصال الأساسي
SELECT @@VERSION;

-- اختبار وجود الجداول
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'dbo';

-- اختبار البيانات الأولية
SELECT COUNT(*) FROM Users;
SELECT COUNT(*) FROM Branches;
```

### اختبار سلامة البيانات
```sql
-- التحقق من سلامة المراجع
SELECT 
    c.CustomerId,
    c.UserId,
    u.UserId
FROM Customers c
LEFT JOIN Users u ON c.UserId = u.UserId
WHERE u.UserId IS NULL;

-- التحقق من أرصدة الحسابات
SELECT 
    a.AccountNumber,
    a.Balance,
    COALESCE(SUM(CASE WHEN t.TransactionType IN (1,3) THEN t.Amount ELSE -t.Amount END), 0) as CalculatedBalance
FROM Accounts a
LEFT JOIN Transactions t ON a.AccountId = t.FromAccountId
GROUP BY a.AccountId, a.AccountNumber, a.Balance
HAVING a.Balance != COALESCE(SUM(CASE WHEN t.TransactionType IN (1,3) THEN t.Amount ELSE -t.Amount END), 0);
```

## 🔄 اختبار الأداء

### اختبار الحمولة
```
سيناريو: معاملات متعددة متزامنة
1. فتح عدة نوافذ للتطبيق
2. تنفيذ معاملات مختلفة في نفس الوقت
3. مراقبة الأداء والاستجابة
النتيجة المتوقعة: عدم تضارب البيانات
```

### اختبار الذاكرة
```
1. تشغيل التطبيق لفترة طويلة
2. تنفيذ عمليات متكررة
3. مراقبة استهلاك الذاكرة
النتيجة المتوقعة: عدم تسريب الذاكرة
```

## 🛡️ اختبار الأمان

### اختبار SQL Injection
```
سيناريو: محاولة حقن SQL
1. في حقل اسم المستخدم، إدخال: admin'; DROP TABLE Users; --
2. محاولة تسجيل الدخول
النتيجة المتوقعة: فشل تسجيل الدخول دون تأثير على قاعدة البيانات
```

### اختبار كلمات المرور
```
سيناريو: التحقق من تشفير كلمات المرور
1. فحص جدول Users في قاعدة البيانات
2. التحقق من عمود PasswordHash
النتيجة المتوقعة: كلمات المرور مشفرة وليست نص واضح
```

## 📱 اختبار واجهة المستخدم

### اختبار الاستجابة
```
1. تغيير حجم النافذة
2. التنقل بين النماذج المختلفة
3. اختبار الأزرار والقوائم
النتيجة المتوقعة: واجهة متجاوبة وسهلة الاستخدام
```

### اختبار اللغة العربية
```
1. التحقق من اتجاه النص (RTL)
2. التحقق من عرض الخطوط العربية
3. اختبار إدخال النصوص العربية
النتيجة المتوقعة: دعم كامل للغة العربية
```

## 📋 قائمة اختبار شاملة

### اختبارات أساسية ✅
- [ ] تسجيل الدخول والخروج
- [ ] التنقل بين القوائم
- [ ] عرض البيانات الأساسية

### اختبارات المعاملات ✅
- [ ] إيداع مبلغ
- [ ] سحب مبلغ
- [ ] تحويل بين الحسابات
- [ ] عرض سجل المعاملات

### اختبارات إدارة العملاء ✅
- [ ] تسجيل عميل جديد
- [ ] فتح حساب جديد
- [ ] تحديث بيانات العميل
- [ ] البحث عن عميل

### اختبارات التقارير ✅
- [ ] تقرير يومي
- [ ] تقرير شهري
- [ ] تقرير العملاء

### اختبارات الأمان ✅
- [ ] تشفير كلمات المرور
- [ ] صلاحيات المستخدمين
- [ ] حماية من SQL Injection

### اختبارات الأداء ✅
- [ ] سرعة الاستجابة
- [ ] استهلاك الذاكرة
- [ ] معالجة الأخطاء

## 🐛 تسجيل الأخطاء

### نموذج تقرير خطأ
```
عنوان الخطأ: [وصف مختصر]
الوصف التفصيلي: [شرح مفصل للمشكلة]
خطوات إعادة الإنتاج:
1. [الخطوة الأولى]
2. [الخطوة الثانية]
3. [الخطوة الثالثة]

النتيجة المتوقعة: [ما كان يجب أن يحدث]
النتيجة الفعلية: [ما حدث فعلاً]
البيئة: [إصدار Windows، .NET، SQL Server]
رسالة الخطأ: [نص رسالة الخطأ إن وجدت]
```

---

**ملاحظة**: يُنصح بتنفيذ هذه الاختبارات في بيئة تطوير منفصلة قبل النشر في بيئة الإنتاج.
